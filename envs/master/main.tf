module "organization" {
  source = "../../local-modules/organizations"

  accounts = [
    {
      name = "sandbox",
      mail = "<EMAIL>",
    },
    {
      name = "development",
      mail = "<EMAIL>",
    },
    {
      name = "staging",
      mail = "<EMAIL>",
    },
    {
      name = "production",
      mail = "<EMAIL>",
    },
    {
      name = "build",
      mail = "<EMAIL>",
    },
    {
      name = "ATOM Advantage",
      mail = "<EMAIL>"
    }
  ]
}

module "ci" {
  source = "../../local-modules/ci/master"
}

module "ci_account" {
  source = "../../local-modules/ci/account"

  master_aws_account_id             = var.aws_account_id
  provisioner_additional_principals = var.provisioner_additional_principals

  sso_trust_enabled = true
  sso_account_id    = var.aws_account_id

}

module "alias" {
  source = "../../local-modules/alias"

  tenant         = var.tenant
  environment    = "master"
  override_alias = var.override_account_alias
}


module "budget" {
  source = "../../local-modules/budget"

  monthly_budget = var.monthly_budget
  alert_mails    = var.alert_mails
  name           = "${var.tenant}-monthly-budget"
}
