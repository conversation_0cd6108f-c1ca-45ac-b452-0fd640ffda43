module "rds_training" {
  source               = "../local-modules/rds"
  identifier           = "${var.rds_name}-training"
  name                 = "${var.rds_name}_training"
  parameter_group_name = "${var.rds_name}-training"
  username             = var.rds_username
  engine               = var.rds_engine
  engine_version       = var.rds_engine_version
  major_engine_version = var.rds_engine_version
  family               = var.rds_family
  port                 = var.rds_port
  multi_az             = var.rds_multi_az
  apply_immediately    = var.apply_immediately
  allocated_storage    = var.rds_allocated_storage
  instance_class       = var.rds_instance_class
  security_group_ids   = [module.sg.id["rds"]]
  subnet_ids           = module.vpc.private_subnets

  parameters = [
    {
      apply_method = "pending-reboot"
      name         = "rds.logical_replication"
      value        = "1"
    },
    {
      apply_method = "immediate"
      name         = "rds.force_ssl"
      value        = "0"
    }
  ]
}
