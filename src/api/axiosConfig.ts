import axios from 'axios';
import { refreshTokenIfNeeded } from './tokenManager';
import { refreshToken } from './tokenRefresh';

let isRefreshing = false;
let failedQueue: any[] = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

export const setupAxiosInterceptors = (logoutFn: () => void) => {
    // Request interceptor
    axios.interceptors.request.use(async (config) => {
        // Skip S3 requests
        if (config.url && (
            config.url.includes('s3.amazonaws.com') ||
            config.url.includes('atom-advantage-packets')
        )) {
            return config;
        }

        // Try to refresh token proactively
        try {
            await refreshTokenIfNeeded();
        } catch (error) {
            console.error("Proactive refresh failed:", error);
        }

        // Get the latest token
        const userAuthStr = localStorage.getItem("userAuth");
        if (userAuthStr) {
            const userAuth = JSON.parse(userAuthStr);
            if (userAuth.token) {
                config.headers.Authorization = `Bearer ${userAuth.token}`;
            }
        }

        return config;
    });

    // Response interceptor with retry queue
    axios.interceptors.response.use(
        (response) => response,
        async (error) => {
            const originalRequest = error.config;
            
            if (error.response?.status === 401 && !originalRequest._retry) {
                if (isRefreshing) {
                    // Queue this request while refreshing
                    return new Promise((resolve, reject) => {
                        failedQueue.push({ resolve, reject });
                    }).then(token => {
                        originalRequest.headers.Authorization = `Bearer ${token}`;
                        return axios(originalRequest);
                    });
                }
                
                originalRequest._retry = true;
                isRefreshing = true;
                
                try {
                    const newToken = await refreshToken();
                    if (newToken) {
                        processQueue(null, newToken);
                        originalRequest.headers.Authorization = `Bearer ${newToken}`;
                        return axios(originalRequest);
                    } else {
                        processQueue(new Error("Token refresh failed"), null);
                        setTimeout(() => logoutFn(), 100); // Slight delay to process queue
                        return Promise.reject(error);
                    }
                } catch (refreshError) {
                    processQueue(refreshError, null);
                    setTimeout(() => logoutFn(), 100);
                    return Promise.reject(error);
                } finally {
                    isRefreshing = false;
                }
            }
            
            return Promise.reject(error);
        }
    );
};