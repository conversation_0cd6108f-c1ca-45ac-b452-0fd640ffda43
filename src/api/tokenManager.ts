import { refreshToken } from './tokenRefresh';

let refreshPromise: Promise<any> | null = null;

export const refreshTokenIfNeeded = async () => {
  const userAuthStr = localStorage.getItem("userAuth");
  if (!userAuthStr) return null;
  
  const userAuth = JSON.parse(userAuthStr);
  const tokenDate = new Date(userAuth.date);
  const now = new Date();
  
  // Check if token is older than 9 minutes (consistent with TokenMonitor)
  // This gives us 6 minutes buffer before the 15-minute expiry
  const tokenAgeMinutes = (now.getTime() - tokenDate.getTime()) / (1000 * 60);
  
  if (tokenAgeMinutes >= 9) {
    // Only create new refresh promise if one isn't already in progress
    if (!refreshPromise) {
      refreshPromise = refreshToken()
        .then(token => {
          if (!token) {
            console.error("Token refresh failed in tokenManager");
          }
          return token;
        })
        .catch(error => {
          console.error("Token refresh error:", error);
          return null;
        })
        .finally(() => {
          refreshPromise = null;
        });
    }
    return refreshPromise;
  }
  
  return null;
};