import axios from "axios";

// Token timing constants - exported for consistency across components
export const TOKEN_EXPIRY_MINUTES = 15;
export const TOKEN_REFRESH_THRESHOLD_MINUTES = 9; // Refresh when token is 9+ minutes old
export const TOKEN_REFRESH_INTERVAL_MINUTES = 8; // Check every 8 minutes

let refreshPromise: Promise<string | null> | null = null;
let lastRefreshTime = 0;
const REFRESH_COOLDOWN = 10000; // 10 seconds

// Export function to check if refresh is in progress
export const isTokenRefreshInProgress = (): boolean => {
  return refreshPromise !== null && (Date.now() - lastRefreshTime < REFRESH_COOLDOWN);
};

// Export function to wait for ongoing refresh
export const waitForTokenRefresh = async (): Promise<void> => {
  if (refreshPromise) {
    await refreshPromise;
  }
};

export const refreshToken = async (): Promise<string | null> => {
  const now = Date.now();
  
  // If we just refreshed, return the existing promise
  if (refreshPromise && (now - lastRefreshTime < REFRESH_COOLDOWN)) {
    console.debug("Token refresh already in progress, returning existing promise");
    return refreshPromise;
  }
  
  // If no refresh in progress, start one
  if (!refreshPromise) {
    refreshPromise = performRefresh()
      .then(token => {
        lastRefreshTime = Date.now();
        return token;
      })
      .finally(() => {
        // Clear promise after cooldown
        setTimeout(() => {
          refreshPromise = null;
        }, REFRESH_COOLDOWN);
      });
  }
  
  return refreshPromise;
};

const performRefresh = async (): Promise<string | null> => {
  const userAuthString = localStorage.getItem("userAuth");
  if (!userAuthString) return null;
  
  try {
    const userAuth = JSON.parse(userAuthString);
    const refresh = userAuth.refresh;
    
    if (!refresh) {
      console.debug("No refresh token available");
      return null;
    }
    
    // Create URLSearchParams for application/x-www-form-urlencoded format
    const formData = new URLSearchParams();
    formData.append('grant_type', 'refresh_token');
    formData.append('refresh_token', refresh);
    
    const API_URL = import.meta.env.VITE_API_URL + "/api/v1";
    
    const response = await axios.post(
      `${API_URL}/auth/token`, 
      formData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'x-api-key': import.meta.env.VITE_API_KEY
        }
      }
    );
    
    if (response.data.access_token) {
      const newUserAuth = {
        ...userAuth,
        token: response.data.access_token,
        refresh: response.data.refresh_token,
        date: new Date().getTime(),
        role: response.data.user_type,
        user_id: response.data.user_id,
      };
      
      localStorage.setItem("userAuth", JSON.stringify(newUserAuth));
      
      // Dispatch events to notify other components
      window.dispatchEvent(new Event("storage"));
      window.dispatchEvent(new CustomEvent("authTokenUpdated", { 
        detail: { token: response.data.access_token } 
      }));
      
      return newUserAuth.token;
    }
    return null;
  } catch (error) {
    console.error("Token refresh failed:", error);
    return null;
  }
};