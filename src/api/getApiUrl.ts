interface GetApiUrl {
  url: string;
  search?: URLSearchParams;
  exclude_base?: boolean;
  params?: GetApiUrlParams;
  index?: string | number;
}

export interface GetApiUrlParams {
  [key: string]: string | string[] | boolean | number | number[];
}

export const getApiUrl = ({
  url,
  search,
  params,
  exclude_base,
  index = "",
}: GetApiUrl): string => {
  const API_URL = import.meta.env.VITE_API_URL;
  const apiURL = new URL(`/api/v1${url}${index ? `/${index}` : ""}`, API_URL);

  if (search) {
    const prepared_search = new URLSearchParams();

    search.forEach((value, key) => {
      if (!["", "null", "undefined"].includes(value))
        prepared_search.set(key, value);
    });

    apiURL.search = prepared_search.toString();
  }

  if (params) {
    const prepared_search = new URLSearchParams();

    for (const [key, value] of Object.entries(params)) {
      if (value === null || value === undefined) continue;

      if (Array.isArray(value)) {
        for (const a_value of value) {
          prepared_search.append(key, a_value.toString());
        }
        continue;
      }

      prepared_search.append(key, value.toString());
    }

    apiURL.search = prepared_search.toString();
  }

  if (exclude_base)
    return `${apiURL.pathname.replace("/api/v1", "")}${apiURL.search}`;

  return apiURL.toString();
};
