import { Close } from "@mui/icons-material";
import UndoIcon from "@mui/icons-material/Undo";
import {
  Box,
  Button,
  FormControl,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import { FC, useCallback, useMemo } from "react";
import { Controller, useFormContext, useWatch } from "react-hook-form";
import { AvailableDocTypes } from "../../../helpers/documentTypes.ts";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";
import { FieldProps, PackageToUpdate } from "../segment.types.ts";
import {
  addMark,
  addTextDecoration,
  getColorByRequired,
} from "./utils/fields.utils.ts";

const styles: StyleCreator<"container" | "button" | "input"> = () => ({
  container: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: "24px",
  },
  input: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: "8px",
  },
  button: {
    height: "40px",
    width: "40px",
    minWidth: "40px",
  },
});

export const SegmentPacketSubtypeField: FC<FieldProps> = ({
  base,
  name,
  label,
  level = 0,
  index,
}) => {
  const c = useStyleCreator(styles);
  const fieldKey = useMemo(() => `${base}.${name}.value`, [base, name]);
  const { setValue, control } = useFormContext();

  const docType = useWatch({
    control,
    name: `predictions.${index}.packet_type`,
  });

  const DropdownFieldValues = useMemo(
    () => AvailableDocTypes[docType] ?? [],
    [docType],
  );

  const { resetField } = useFormContext<PackageToUpdate>();
  const handleReset = useCallback(
    () => resetField(fieldKey as "metadata"),
    [fieldKey, resetField],
  );
  const handleClear = useCallback(
    () => setValue(fieldKey as "metadata", ""),
    [fieldKey, setValue],
  );

  return (
    <Controller
      control={control}
      name={fieldKey}
      render={({ field: { value, onChange, ref } }) => {
        return (
          <Box sx={c.container}>
            <Typography
              textTransform={"capitalize"}
              style={{
                minWidth: "35%",
                paddingLeft: `${level * 40}px`,
                color: getColorByRequired(fieldKey),
                textDecoration: addTextDecoration(fieldKey),
              }}
              fontWeight={value ? 500 : 700}
            >
              {label}
              {addMark(fieldKey)}:
            </Typography>
            <Box sx={c.input}>
              <FormControl ref={ref} size={"small"} fullWidth>
                <Select
                  displayEmpty
                  size={"small"}
                  id={fieldKey}
                  value={value}
                  onChange={(event) => onChange(event.target.value)}
                  renderValue={(value) => {
                    if (value === "")
                      return (
                        <Typography sx={{ opacity: "0.5", lineHeight: "1.4" }}>
                          Select {label}
                        </Typography>
                      );
                    return <>{value}</>;
                  }}
                >
                  {DropdownFieldValues.map((item) => (
                    <MenuItem key={item} value={item}>
                      {item}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <Button
                tabIndex={-1}
                sx={c.button}
                variant={"outlined"}
                onClick={handleClear}
              >
                <Close />
              </Button>
              <Button
                tabIndex={-1}
                sx={c.button}
                variant={"outlined"}
                onClick={handleReset}
              >
                <UndoIcon />
              </Button>
            </Box>
          </Box>
        );
      }}
    />
  );
}; 