import {
  <PERSON>ton,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Tooltip,
} from "@mui/material";
import { FC, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useAuthContext } from "../../../hooks/useAuth.ts";
import { ResponseError } from "../../../store/queries/users/types/User.ts";
import { useResetPassword } from "../../../store/queries/users/users.query.ts";

interface Props {
  open: boolean;
  handleClose: () => void;
}

const EmailDialog: FC<{ open: boolean }> = ({ open }) => {
  const { logout } = useAuthContext();

  const [countdown, setCountdown] = useState(3);
  const intervalRef = useRef(null);

  useEffect(() => {
    if (countdown <= 0) {
      window.clearInterval(intervalRef.current);
      logout();
    }
  }, [countdown, logout]);

  useEffect(() => {
    if (open) {
      intervalRef.current = window.setInterval(() => {
        setCountdown((prevState) => prevState - 1);
      }, 1000);
    }

    return () => {
      window.clearInterval(intervalRef.current);
    };
  }, [open]);

  return (
    <Dialog open={open}>
      <DialogContent>
        <DialogTitle>Confirmation link was sent to your email</DialogTitle>
        <DialogContentText typography={"caption"} sx={{ textAlign: "center" }}>
          Session will be close in {countdown}
        </DialogContentText>
      </DialogContent>
    </Dialog>
  );
};

export const ResetPasswordModal: FC<Props> = ({ open, handleClose }) => {
  const { username } = useAuthContext();

  const [resetPassword, { isError, error, isLoading }] = useResetPassword();

  const [emailSent, setEmailSent] = useState(false);

  const errorMessage = useMemo(() => {
    if (!isError) return null;
    const typedError = error as ResponseError;

    if (!typedError.data) return "Unknown error";

    if (typedError.data.detail instanceof Array) {
      return typedError.data.detail.map(({ msg }) => msg);
    }

    return typedError.data.detail;
  }, [error, isError]);

  const handleResetPassword = useCallback(() => {
    resetPassword({ email: username.trim() })
      .unwrap()
      .then(() => {
        handleClose();
        setEmailSent(true);
      });
  }, [handleClose, resetPassword, username]);

  return (
    <>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Confirm password reset</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to reset your password?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            sx={{ height: "38px" }}
            onClick={handleClose}
            variant={"outlined"}
          >
            No
          </Button>
          <Tooltip title={isError ? errorMessage : null}>
            <Button
              sx={{ height: "38px" }}
              disabled={isLoading}
              onClick={handleResetPassword}
              variant={"contained"}
              autoFocus
              color={isError ? "error" : "primary"}
            >
              {isLoading && <CircularProgress size={26} />}
              {!isLoading && "Yes"}
            </Button>
          </Tooltip>
        </DialogActions>
      </Dialog>
      <EmailDialog open={emailSent} />
    </>
  );
};
