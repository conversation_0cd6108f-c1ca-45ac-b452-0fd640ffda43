import { Visibility, VisibilityOff } from "@mui/icons-material";
import { IconButton, TextField, Tooltip, TooltipProps } from "@mui/material";
import { FC, useState } from "react";
import { Controller } from "react-hook-form";

interface Props extends Pick<TooltipProps, "placement"> {
  name: string;
  label: string;
}

export const UserPasswordField: FC<Props> = ({ name, label, placement }) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <Controller
      name={name}
      render={({ field: { value, onChange, ref }, fieldState }) => {
        return (
          <Tooltip
            title={fieldState.error?.message ?? null}
            placement={placement}
          >
            <TextField
              type={isVisible ? "text" : "password"}
              label={label}
              value={value}
              onChange={onChange}
              ref={ref}
              size={"small"}
              fullWidth
              error={fieldState.invalid && fieldState.isDirty}
              InputProps={{
                endAdornment: (
                  <IconButton
                    size={"small"}
                    onMouseDown={() => setIsVisible(true)}
                    onMouseUp={() => setIsVisible(false)}
                  >
                    {isVisible ? <Visibility /> : <VisibilityOff />}
                  </IconButton>
                ),
              }}
            />
          </Tooltip>
        );
      }}
    />
  );
};
