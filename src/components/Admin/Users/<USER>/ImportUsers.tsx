import { Upload } from "@mui/icons-material";
import {
  Box,
  Button,
  CircularProgress,
  SxProps,
  Tooltip,
  Typography,
  styled,
} from "@mui/material";
import parseCSV from "convert-csv-to-json";
import {
  FC,
  SyntheticEvent,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useDropzone } from "react-dropzone";
import { useImportUser } from "../../../../store/queries/users/users.query.ts";
import { CreateUsersErrorResponse } from "../utils/CreateUsersErrorResponse.ts";
import { ErrorTooltip } from "../utils/ErrorTooltip.tsx";
import { UsersFormValidationSchema } from "../utils/UsersFormValidationSchema.ts";
import { DownloadTemplate } from "./DownloadTemplate.tsx";

const FileBox = styled(Box)<{ error: boolean; success: boolean }>(({
  theme,
  error,
  success,
}) => {
  const base: SxProps = {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: "8px",
    minWidth: "100%",
    padding: "32px",
    border: "1px solid",
    borderRadius: "4px",
    cursor: "pointer",
    "&:hover": {
      borderColor: theme.palette.primary.main,
      boxShadow: "3",
    },
  };

  if (error)
    Object.assign(base, {
      borderColor: `${theme.palette.error.main} !important`,
    });

  if (success)
    Object.assign(base, {
      borderColor: `${theme.palette.success.main} !important`,
    });

  return base;
});

type ValidationDetails = {
  [key: string]: { message: string };
};

const FileValidator = (file: File) => {
  if (file.type !== "text/csv") {
    return {
      code: "wrong-file-format",
      message: `Only .csv files can be uploaded.`,
    };
  }

  return null;
};

export const ImportUsers = () => {
  const [importUsers, { isLoading, isError, error, isSuccess }] =
    useImportUser();

  const [file, setFile] = useState<File | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [responseMessage, setResponseMessage] = useState("");
  const [validationDetails, setValidationDetails] =
    useState<ValidationDetails | null>(null);

  const onDrop = useCallback(
    (acceptedFiles) => {
      const file = acceptedFiles[0];
      setFile(file);
    },
    [setFile],
  );

  const { getRootProps, getInputProps, fileRejections, acceptedFiles } =
    useDropzone({
      onDrop,
      maxFiles: 1,
      validator: FileValidator,
    });

  const isFileValid = useMemo(
    () => fileRejections.length === 0,
    [fileRejections],
  );
  const acceptedFileName = useMemo(() => {
    if (acceptedFiles.length === 0) return null;

    return acceptedFiles[0].name;
  }, [acceptedFiles]);

  const errors = useMemo(() => {
    if (!isError) return null;

    const typedError = error as {
      data: { detail: CreateUsersErrorResponse | string };
    };
    return typedError.data.detail;
  }, [error, isError]);

  const TooltipTitle = useMemo(() => {
    if (validationDetails)
      return <ValidationTooltip validation={validationDetails} />;
    if (errors && typeof errors === "string") {
      return errors;
    } else if (errors && errors instanceof Object) {
      return <ErrorTooltip {...errors} />;
    }

    return null;
  }, [errors, validationDetails]);

  const validate = useCallback(async (file: File) => {
    const csvText = await file.text();

    try {
      const users = parseCSV
        .fieldDelimiter(",")
        .csvStringToJson(csvText)
        .map((user) => {
          return {
            ...user,
            allowed_clients: user.allowed_clients.split(";").filter(Boolean),
          };
        });
      return {
        is_valid: true,
        data: await UsersFormValidationSchema.validate(
          { users },
          { abortEarly: false },
        ),
      };
    } catch (errors) {
      if (errors.name !== "ValidationError") {
        return {
          is_valid: false,
          details: {
            file: { message: "file shouldn't be empty" },
          },
        };
      }

      return {
        is_valid: false,
        details: errors.inner.reduce(
          (allErrors, currentError) => ({
            ...allErrors,
            [currentError.path]: {
              type: currentError.type ?? "validation",
              message: currentError.message,
            },
          }),
          {},
        ),
      };
    }
  }, []);

  const upload = useCallback(
    async (event: SyntheticEvent) => {
      event.stopPropagation();
      setIsValidating(true);
      const { is_valid, details } = await validate(file);

      if (!is_valid) {
        setIsValidating(false);
        return setValidationDetails(details);
      } else {
        const data = new FormData();
        data.append("csv_file", file);
        importUsers(data)
          .unwrap()
          .then(() => {
            setResponseMessage("Success");
          });
        setIsValidating(false);
      }
    },
    [file, importUsers, validate],
  );

  useEffect(() => {
    setValidationDetails(null);
  }, [file]);

  return (
    <Tooltip title={TooltipTitle}>
      <FileBox
        error={isError || !isFileValid || Boolean(validationDetails)}
        success={isSuccess && !error && !validationDetails}
        {...getRootProps()}
      >
        <input {...getInputProps()} name={"file"} id={"file"} />
        <Typography>Drag & Drop here or click to select file</Typography>
        {acceptedFileName && (
          <Typography
            sx={{ margin: "auto" }}
            typography={"caption"}
            fontWeight={700}
          >
            {acceptedFileName}
          </Typography>
        )}
        <DownloadTemplate />
        <Button
          sx={{ height: "38px", width: "125px" }}
          onClick={upload}
          disabled={!file || isLoading || isValidating}
          variant={"contained"}
          startIcon={!isLoading && !isValidating && <Upload />}
        >
          {isLoading || isValidating ? (
            <CircularProgress size={24} />
          ) : (
            "Upload"
          )}
        </Button>
      </FileBox>
    </Tooltip>
  );
};

function parseFiledName(
  filed: string,
): { filed_name: string; index: number } | null {
  const regex = /^(\w+)\[(\d+)\]$/;
  const match = filed.match(regex);

  return {
    filed_name: match[1].slice(0, match[1].length - 1),
    index: parseInt(match[2], 10),
  };
}

const ValidationTooltip: FC<{
  validation: ValidationDetails | null;
}> = ({ validation }) => {
  const { type, results, message } = useMemo<{
    type: "global" | "nested";
    message?: string;
    results: { [key: string]: string[] };
  }>(() => {
    if (!validation) return { type: "global", results: {} };

    if (validation["users"]) {
      return {
        type: "global",
        message: validation["users"].message,
        results: {},
      };
    }

    if (validation["file"]) {
      return {
        type: "global",
        message: validation["file"].message,
        results: {},
      };
    }

    return {
      type: "nested",
      results: Object.entries(validation).reduce((acc, [key, { message }]) => {
        const [row] = key.split(".");
        const { filed_name, index } = parseFiledName(row);

        if (acc[row]) {
          return {
            ...acc,
            [`${filed_name} ${index + 1}:`]: [...acc[row], `${message}`],
          };
        } else {
          return { ...acc, [`${filed_name} ${index + 1}:`]: [`${message}`] };
        }
      }, {}),
    };
  }, [validation]);

  if (validation)
    return (
      <Box sx={{ display: "flex", flexDirection: "column", gap: "12px" }}>
        {type === "global" && (
          <Typography typography={"caption"}>{message}</Typography>
        )}
        {type === "nested" &&
          Object.entries(results).map(([row, fields]) => (
            <Box sx={{ display: "flex", flexDirection: "column", gap: "4px" }}>
              <Typography
                typography={"caption"}
                fontWeight={700}
                textTransform={"capitalize"}
              >
                {row}
              </Typography>
              <Box sx={{ marginLeft: "18px" }}>
                {(fields as string[]).map((field) => (
                  <Typography typography={"caption"}>{field}</Typography>
                ))}
              </Box>
            </Box>
          ))}
      </Box>
    );

  return null;
};
