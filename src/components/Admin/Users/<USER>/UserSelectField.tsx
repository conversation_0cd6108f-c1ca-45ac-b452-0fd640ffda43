import {
  Checkbox,
  FormControl,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  Tooltip,
} from "@mui/material";
import { FC } from "react";
import { Controller } from "react-hook-form";

interface Props {
  name: string;
  label: string;
  multiple?: boolean;
  items: { value: string; text: string }[];
}

export const UserSelectField: FC<Props> = ({
  name,
  label,
  multiple,
  items,
}) => {
  return (
    <Controller
      name={name}
      render={({
        field: { value, onChange, ref },
        fieldState: { error, invalid, isDirty },
      }) => {
        return (
          <Tooltip title={error?.message ?? null}>
            <FormControl fullWidth>
              <InputLabel size={"small"} id={label}>
                {label}
              </InputLabel>
              {!multiple && (
                <Select
                  label={label}
                  value={value}
                  onChange={onChange}
                  ref={ref}
                  id={`${name}-${label}`}
                  labelId={label}
                  input={<OutlinedInput label={label} size={"small"} />}
                  error={invalid && isDirty}
                  variant={"outlined"}
                  renderValue={(value) =>
                    items.find((item) => value === item.value).text
                  }
                >
                  {items.map(({ text, value: rowValue }) => {
                    return (
                      <MenuItem value={rowValue}>
                        <Checkbox disableRipple checked={value === rowValue} />
                        {text}
                      </MenuItem>
                    );
                  })}
                </Select>
              )}
              {multiple && (
                <Select
                  label={label}
                  variant={"outlined"}
                  value={value}
                  onChange={onChange}
                  ref={ref}
                  id={`${name}-${label}`}
                  labelId={label}
                  multiple
                  input={<OutlinedInput label={label} size={"small"} />}
                  renderValue={(selected: string[]) =>
                    selected
                      .map(
                        (item) =>
                          items.find(({ value }) => value === item).text,
                      )
                      .join(", ")
                  }
                  error={invalid}
                >
                  {items.map(({ text, value: rowValue }) => {
                    return (
                      <MenuItem value={rowValue}>
                        <Checkbox
                          disableRipple
                          checked={value.includes(rowValue)}
                        />
                        {text}
                      </MenuItem>
                    );
                  })}
                </Select>
              )}
            </FormControl>
          </Tooltip>
        );
      }}
    />
  );
};
