import { Download } from "@mui/icons-material";
import {
  Box,
  Button,
  CircularProgress,
  Tooltip,
  Typography,
} from "@mui/material";
import axios from "axios";
import { useCallback, useMemo, useState } from "react";
import { getApiUrl } from "../../../../api/getApiUrl.ts";
import { useAuthContext } from "../../../../hooks/useAuth.ts";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../../hooks/useStyleCreator.ts";

const styles: StyleCreator<"container"> = (theme) => ({
  container: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "100%",
    padding: "32px",
    border: "1px solid",
    borderRadius: "4px",
  },
});

export const ExportUsers = () => {
  const { token } = useAuthContext();
  const c = useStyleCreator(styles);

  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isError, setIsError] = useState(false);
  const [responseMessage, setResponseMessage] = useState<string | null>(null);

  const statusColor = useMemo(() => {
    switch (true) {
      case isLoading:
        return "primary";
      case isError:
        return "error";
      case isSuccess:
        return "success";
      default:
        return "primary";
    }
  }, [isError, isLoading, isSuccess]);

  const FetchCSV = useCallback(async () => {
    try {
      const data = await axios.get(getApiUrl({ url: "/users/all/export" }), {
        headers: {
          Authorization: `Bearer ${token}`,
          "x-api-key": import.meta.env.VITE_API_KEY,
        },
        responseType: "blob",
      });

      return data.data;
    } catch (e) {
      return e;
    }
  }, [token]);

  const handleClick = useCallback(() => {
    setIsError(false);
    setIsSuccess(false);
    setResponseMessage(null);
    setIsLoading(true);
    FetchCSV()
      .then((response) => {
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");

        link.href = href;
        link.setAttribute("download", "users.csv");
        document.body.appendChild(link);
        link.click();

        document.body.removeChild(link);
        URL.revokeObjectURL(href);

        setIsSuccess(true);
        setResponseMessage("Success");
      })
      .catch((e) => {
        console.log(e);
        setIsError(true);
        setResponseMessage("Server error occurred");
      })
      .finally(() => setIsLoading(false));
  }, [FetchCSV]);

  return (
    <Box sx={c.container}>
      <Typography>Export all user and user details as a CSV file</Typography>
      <Tooltip title={responseMessage}>
        <Button
          onClick={handleClick}
          sx={{ height: "38px", width: "125px" }}
          variant={"contained"}
          startIcon={!isLoading && <Download />}
          disabled={isLoading}
          color={statusColor}
        >
          {!isLoading && "EXPORT"}
          {isLoading && <CircularProgress size={24} />}
        </Button>
      </Tooltip>
    </Box>
  );
};
