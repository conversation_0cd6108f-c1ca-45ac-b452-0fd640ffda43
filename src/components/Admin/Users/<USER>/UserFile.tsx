import { Box, Paper, Typography, darken } from "@mui/material";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../../hooks/useStyleCreator.ts";
import { ExportUsers } from "./ExportUsers.tsx";
import { ImportUsers } from "./ImportUsers.tsx";

const styles: StyleCreator<"container" | "section" | "box"> = (theme) => ({
  container: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    gap: " 12px",
    [theme.breakpoints.down(1440)]: {
      flexDirection: "column",
    },
  },
  section: {
    position: "relative",
    width: "100%",
    padding: "128px 128px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: "32px",
    background: darken("#607274", 0.07),
  },
  box: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: "100%",
    padding: "32px",
    border: "1px solid",
    borderRadius: "4px",
  },
});

export const UserFile = () => {
  const c = useStyleCreator(styles);

  return (
    <Box sx={c.container}>
      <Paper sx={c.section}>
        <Typography
          sx={{
            position: "absolute",
            top: "calc((128px / 2) - (56px / 2))",
          }}
          variant="h3"
        >
          Import/Export Users
        </Typography>
        <ImportUsers />
        <ExportUsers />
      </Paper>
    </Box>
  );
};
