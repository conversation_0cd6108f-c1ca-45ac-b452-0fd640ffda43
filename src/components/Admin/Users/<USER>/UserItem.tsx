import { Remove } from "@mui/icons-material";
import { Box, Button, Grow, Typography } from "@mui/material";
import { FC, SyntheticEvent, useCallback, useState } from "react";
import { UseFieldArrayRemove, useFormContext } from "react-hook-form";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../../hooks/useStyleCreator.ts";
import { PreparedClients } from "../../../../store/queries/clients/types/Client.ts";
import { UserSelectField } from "./UserSelectField.tsx";
import { UserTextField } from "./UserTextField.tsx";

const styles: StyleCreator<"container" | "header" | "button" | "row"> = (
  theme,
) => ({
  container: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    gap: "12px",
    border: "1px solid",
    borderColor: theme.palette.primary.main,
    borderRadius: "4px",
  },
  header: {
    width: "100%",
    height: "40px",
    backgroundColor: theme.palette.primary.main,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: "12px",
    borderRadius: "4px",
    cursor: "pointer",
  },
  button: {
    minWidth: "40px",
    "& .MuiSvgIcon-root": {
      color: theme.palette.primary.contrastText,
    },
  },
  row: {
    display: "flex",
    alignItems: "center",
    width: "100%",
    gap: "8px",
  },
});

interface Props {
  index: number;
  removeUser: UseFieldArrayRemove;
  clients: PreparedClients;
}

export const UserItem: FC<Props> = ({ index, removeUser, clients }) => {
  const c = useStyleCreator(styles);

  const { formState, getFieldState } = useFormContext();
  const { invalid } = getFieldState(`users.${index}`, formState);

  const [open, setOpen] = useState(false);

  const remove = useCallback(
    (event: SyntheticEvent) => {
      event.stopPropagation();
      removeUser(index);
    },
    [index, removeUser],
  );

  return (
    <Box sx={c.container}>
      <Box sx={c.header} onClick={() => setOpen((prev) => !prev)}>
        <Typography
          color={invalid ? "error.main" : "primary.contrastText"}
          typography={"body1"}
        >
          User № {index + 1}
        </Typography>
        <Button sx={c.button} onClick={remove}>
          <Remove />
        </Button>
      </Box>
      {open && (
        <Grow in={open}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: "12px",
              padding: "12px",
            }}
          >
            <Box sx={c.row}>
              <UserTextField name={`users.${index}.email`} label={"Email"} />
              <UserTextField
                name={`users.${index}.username`}
                label={"Username"}
              />
            </Box>
            <Box sx={c.row}>
              <UserTextField
                name={`users.${index}.given_name`}
                label={"First Name"}
              />
              <UserTextField
                label={"Last Name"}
                name={`users.${index}.family_name`}
              />
            </Box>
            <Box sx={c.row}>
              <UserSelectField
                label={"Role"}
                name={`users.${index}.user_type`}
                items={[
                  { text: "User", value: "user" },
                  { text: "Delegate", value: "delegate" },
                  { text: "Manager", value: "manager" },
                ]}
              />
              <UserSelectField
                multiple
                label={"Available clients"}
                name={`users.${index}.allowed_clients`}
                items={clients}
              />
            </Box>
          </Box>
        </Grow>
      )}
    </Box>
  );
};
