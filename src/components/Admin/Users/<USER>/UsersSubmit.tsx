import { Box, Button, CircularProgress, Tooltip } from "@mui/material";
import { FC, useMemo } from "react";
import { useFormContext } from "react-hook-form";
import { CreateUsersErrorResponse } from "../utils/CreateUsersErrorResponse.ts";
import { ErrorTooltip } from "../utils/ErrorTooltip.tsx";

export const UsersSubmit: FC<{
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  error: any;
}> = ({ isLoading, isError, isSuccess, error }) => {
  const {
    formState: { isValid },
  } = useFormContext();

  const errors = useMemo(() => {
    if (!isError) return null;
    console.log(error);
    return error as CreateUsersErrorResponse | string;
  }, [error, isError]);

  const TooltipTitle = useMemo(() => {
    if (errors && typeof errors === "string") {
      return errors;
    } else if (errors && errors instanceof Object) {
      return <ErrorTooltip {...errors} />;
    }

    return null;
  }, [errors]);

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        width: "100%",
        gap: "8px",
      }}
    >
      <Button type={"reset"} fullWidth variant={"outlined"}>
        Cancel
      </Button>
      <Tooltip title={TooltipTitle}>
        <Button
          disabled={!isValid || isLoading}
          type={"submit"}
          fullWidth
          variant={"contained"}
          color={isError ? "error" : "primary"}
        >
          {isLoading && <CircularProgress size={24} />}
          {!isLoading && isError && "Invite"}
          {!isLoading && isSuccess && "Successful"}
          {!isLoading && !isSuccess && !isError && "Invite"}
        </Button>
      </Tooltip>
    </Box>
  );
};
