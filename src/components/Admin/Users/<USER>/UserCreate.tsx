import { yupResolver } from "@hookform/resolvers/yup";
import { Box, Button, darken, Paper, Typography } from "@mui/material";
import { useCallback } from "react";
import { FormProvider, useFieldArray, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../../hooks/useStyleCreator.ts";
import { useClients } from "../../../../store/queries/clients/clients.query.ts";
import { useInviteUsers } from "../../../../store/queries/users/users.query.ts";
import { FormValidator } from "../utils/FormValidator.tsx";
import { UsersFormValidationSchema } from "../utils/UsersFormValidationSchema.ts";
import { UserItem } from "./UserItem.tsx";
import { UsersSubmit } from "./UsersSubmit.tsx";

const styles: StyleCreator<"container" | "section" | "box"> = (theme) => ({
  container: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    height: "100%",
    [theme.breakpoints.down(1440)]: {
      height: "100vh",
    },
    maxHeight: "885px",
    gap: " 12px",
    [theme.breakpoints.down(1440)]: {
      flexDirection: "column",
    },
  },
  section: {
    width: "100%",
    padding: "24px 128px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: "24px",
    height: "100%",
    background: darken("#607274", 0.07),
    [theme.breakpoints.down(1440)]: {
      height: "100%",
      maxHeight: "85vh",
    },
  },
  box: {
    display: "flex",
    flexDirection: "column",
    gap: "12px",
    minWidth: "100%",
    padding: "24px",
    border: "1px solid",
    height: "100%",
    overflowY: "scroll",
    borderRadius: "4px",
  },
});

export const UserCreate = () => {
  const c = useStyleCreator(styles);

  const { data: clients, isLoading: isClientsLoading } = useClients();
  const [inviteUsers, { error, isError, isSuccess, isLoading }] =
    useInviteUsers();

  const FormMethods = useForm({
    defaultValues: { users: [] },
    resolver: yupResolver(UsersFormValidationSchema),
  });
  const { fields, append, remove } = useFieldArray({
    control: FormMethods.control,
    name: "users",
  });

  const handleAdd = useCallback(() => {
    append({
      allowed_clients: [],
      family_name: "",
      user_type: "user",
      username: "",
      given_name: "",
      email: "",
    });
  }, [append]);

  const handleInvite = useCallback(
    (data) => {
      inviteUsers(data)
        .unwrap()
        .then(() => {
          FormMethods.reset();
          toast.success(`Invited ${data.users.length} user(-s)`, {
            position: "bottom-right",
          });
        });
    },
    [FormMethods, inviteUsers],
  );

  return (
    <FormProvider {...FormMethods}>
      <FormValidator schema={UsersFormValidationSchema} />
      <Box
        component={"form"}
        onSubmit={FormMethods.handleSubmit(handleInvite)}
        onReset={() => FormMethods.reset()}
        sx={c.container}
      >
        <Paper sx={c.section}>
          <Typography variant="h3">Invite Users</Typography>
          <Box sx={c.box}>
            {fields.map((field, id) => (
              <UserItem
                clients={isClientsLoading ? [] : clients}
                key={field.id}
                index={id}
                removeUser={remove}
              />
            ))}
          </Box>
          <Button onClick={handleAdd} fullWidth variant={"contained"}>
            Add User
          </Button>
          <UsersSubmit
            isLoading={isLoading}
            isError={isError}
            isSuccess={isSuccess}
            error={error}
          />
        </Paper>
      </Box>
    </FormProvider>
  );
};
