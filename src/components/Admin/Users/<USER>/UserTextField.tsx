import { TextField, Tooltip, TooltipProps } from "@mui/material";
import { FC } from "react";
import { Controller } from "react-hook-form";

interface Props extends Pick<TooltipProps, "placement"> {
  name: string;
  label: string;
  shrink?: boolean;
}

export const UserTextField: FC<Props> = ({
  name,
  label,
  placement,
  shrink,
}) => {
  return (
    <Controller
      name={name}
      render={({ field: { value, onChange, ref }, fieldState }) => {
        return (
          <Tooltip
            title={fieldState.error?.message ?? null}
            placement={placement}
          >
            <TextField
              type={"text"}
              label={label}
              value={value === null ? "" : value}
              onChange={onChange}
              ref={ref}
              size={"small"}
              fullWidth
              error={fieldState.invalid && fieldState.isDirty}
              InputLabelProps={{ shrink }}
            />
          </Tooltip>
        );
      }}
    />
  );
};
