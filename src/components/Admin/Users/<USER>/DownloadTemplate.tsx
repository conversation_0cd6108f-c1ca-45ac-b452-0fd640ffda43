import { Download } from "@mui/icons-material";
import { Button, CircularProgress, Tooltip } from "@mui/material";
import axios from "axios";
import { SyntheticEvent, useCallback, useState } from "react";

export const DownloadTemplate = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [responseMessage, setResponseMessage] = useState<string | null>(null);

  const FetchTemplate = useCallback(async () => {
    try {
      const data = await axios.get("/users_template.csv", {
        responseType: "blob",
      });

      return data.data;
    } catch (e) {
      return e;
    }
  }, []);

  const handleClick = useCallback(
    (event: SyntheticEvent) => {
      event.stopPropagation();
      setResponseMessage(null);
      setIsLoading(true);
      FetchTemplate()
        .then((response) => {
          const href = URL.createObjectURL(response);
          const link = document.createElement("a");

          link.href = href;
          link.setAttribute("download", "users_template.csv");
          document.body.appendChild(link);
          link.click();

          document.body.removeChild(link);
          URL.revokeObjectURL(href);
        })
        .catch(() => {
          setResponseMessage("Error occurred");
        })
        .finally(() => setIsLoading(false));
    },
    [FetchTemplate],
  );

  return (
    <Tooltip title={responseMessage}>
      <Button
        sx={{ minWidth: "166px", height: "38px", marginLeft: "auto" }}
        onClick={handleClick}
        disabled={isLoading}
        variant={"contained"}
        startIcon={!isLoading && <Download />}
      >
        {isLoading ? <CircularProgress size={24} /> : "Get Template"}
      </Button>
    </Tooltip>
  );
};
