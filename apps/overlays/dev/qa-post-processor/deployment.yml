---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qa-post-processor
  namespace: apps
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 1
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: qa-post-processor
  template:
    metadata:
      labels:
        app: qa-post-processor
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - qa-tool
      tolerations:
        - key: "qa-tool"
          operator: Equal
          value: "true"
          effect: "NoExecute"
      serviceAccountName: qa-post-processor
      # volumes:
      #   - name: secrets-store-inline
      #     csi:
      #       driver: secrets-store.csi.k8s.io
      #       readOnly: true
      #       volumeAttributes:
      #         secretProviderClass: downloader
      dnsPolicy: ClusterFirst
      
      containers:
        - name: qa-post-processor
          image: ************.dkr.ecr.us-east-2.amazonaws.com/qa-post-processor:3f97c6bd994bcb705dac6b77ea463533d2e4ed57
          imagePullPolicy: Always
          resources:
            limits:
              memory: 1000Mi
              cpu: 500m
            requests:
              memory: 200Mi
              cpu: 200m
          env:
            - name: MINIO_URI
              value: "minio.minio.svc.cluster.local:9000"
            - name: MINIO_ACCESS_KEY
              value: "YkDLavztCoiaMx5rpo32"
            - name: MINIO_SECRET_KEY
              value: "uA2IrRU9GoaI7BHSG2kfDvXtujgQiE6CqI9LaUrs"
            - name: MINIO_FILES_BUCKET
              value: "atom-advantage-packets-dev"
            - name: MINIO_SECURE
              value: "false"
            - name: SFTP_REMOTE_HOST
              value: "localhost"
            - name: SFTP_REMOTE_PORT
              value: "222"
            - name: SFTP_REMOTE_USER
              value: "sftp"
            - name: SFTP_REMOTE_PASSWORD
              value: "test"
            - name: SFTP_REMOTE_PATH
              value: "folder"
            - name: PROJECT_NAME
              value: "atom-advantage-packets-dev"
            - name: APP_NAME
              value: "qa-post-processor"
            - name: REMOTE_RABBITMQ_HOST
              value: "rabbitmq.rabbitmq.svc.cluster.local"
            - name: REMOTE_RABBITMQ_PORT
              value: "5672"
            - name: REMOTE_RABBITMQ_USERNAME
              value: "user"
            - name: REMOTE_RABBITMQ_PASSWORD
              value: "Awwmm8FDzgcShYXS"
            - name: REMOTE_RABBITMQ_DEFAULT_VHOST
              value: "/"  
            - name: REMOTE_RABBITMQ_TO_BACKEND_QA_QUEUE_NAME
              value: "to_backend_qa"
            - name: REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME
              value: "from_backend_qa"
            - name: REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME
              value: "packet_status"
            - name: REMOTE_RABBITMQ_FROM_BACKEND_SCREENSHOT_QUEUE_NAME
              value: "from_backend_screenshot"
            - name: RABBITMQ_TO_CLASSIFY_QUEUE_NAME
              value: "to_classify"
            - name: RABBITMQ_TO_SPLIT_QUEUE_NAME
              value: "to_split"
            - name: RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME
              value: "to_extract_metadata"
            - name: RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME
              value: "to_metadata_postprocess"
            - name: RABBITMQ_TO_VALIDATE_QUEUE_NAME
              value: "to_validate"
            - name: RABBITMQ_TO_QA_POSTPROCESS_QUEUE_NAME
              value: "to_qa_postprocess"
            - name: RABBITMQ_TO_UPLOAD_QUEUE_NAME
              value: "to_upload"
            - name: RABBITMQ_TO_SCREENSHOT_QUEUE_NAME
              value: "to_screenshot"
            - name: RABBITMQ_TO_SCREENSHOT_POSTPROCESS_QUEUE_NAME
              value: "to_screenshot_postprocess"
            - name: REMOTE_RABBITMQ_QUEUE_SUB_PACKET_INCOMING
              value: "RABBITMQ_QUEUE_SUB_PACKET_INCOMING"
            - name: REMOTE_RABBITMQ_QUEUE_SUB_PACKET_SKIPPED_INCOMING
              value: "RABBITMQ_QUEUE_SUB_PACKET_SKIPPED_INCOMING"
            - name: REMOTE_RABBITMQ_QUEUE_SUB_PACKET_STATUS
              value: "RABBITMQ_QUEUE_SUB_PACKET_STATUS"
            - name: REMOTE_RABBITMQ_QUEUE_SUB_PACKET_SCREENSHOT
              value: "RABBITMQ_QUEUE_SUB_PACKET_SCREENSHOT"
            - name: REMOTE_RABBITMQ_QUEUE_PUB
              value: "RABBITMQ_QUEUE_PUB"
            - name: RABBITMQ_HOST
              value: "rabbitmq.rabbitmq.svc.cluster.local"
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_USERNAME
              value: "user"
            - name: RABBITMQ_PASSWORD
              value: "Awwmm8FDzgcShYXS"
            - name: RABBITMQ_DEFAULT_VHOST
              value: "/"
            - name: RABBITMQ_QUEUE_SUB_PACKET_INCOMING
              value: "RABBITMQ_QUEUE_SUB_PACKET_INCOMING"
            - name: RABBITMQ_QUEUE_SUB_PACKET_SKIPPED_INCOMING
              value: "RABBITMQ_QUEUE_SUB_PACKET_SKIPPED_INCOMING"
            - name: RABBITMQ_QUEUE_SUB_PACKET_STATUS
              value: "RABBITMQ_QUEUE_SUB_PACKET_STATUS"
            - name: RABBITMQ_QUEUE_SUB_PACKET_SCREENSHOT
              value: "RABBITMQ_QUEUE_SUB_PACKET_SCREENSHOT"
            - name: RABBITMQ_QUEUE_PUB
              value: "RABBITMQ_QUEUE_PUB"
            - name: API_HOST
              value: "qa-backend.apps.svc.cluster.local"
            - name: API_PORT
              value: "80"
            - name: SERVICE_BUS_CONNECTION_STRING
              value: "Endpoint=sb://atom-advantage-dev-service-bus-1.servicebus.windows.net/;SharedAccessKeyName=kamil;SharedAccessKey=MQGjhOgipRmUEZIATcu1hFHTrUlNOFfgu+ASbKulQ10=;EntityPath=sbq-devstack-devname-use-dev-atom-advantage-dev-service-bus-1-queue-1"
            - name: TOPIC_NAME
              value: "splitter-dev"
            - name: SUBSCRIPTION_NAME
              value: "08bcb323-cbf9-4ab1-b6cf-12dcd17bb8ce"
            - name: SERVICE_BUS_QUEUE_NAME
              value: "sbq-devstack-devname-use-dev-atom-advantage-dev-service-bus-1-queue-1"
            - name: S3_BUCKET_NAME
              value: "atom-advantage-packets-dev"
            - name: S3_REGION
              value: "us-east-2"
            - name: S3_ENDPOINT_URL
              value: "https://atom-advantage-packets-dev.s3.us-east-2.amazonaws.com"
            - name: PGSQL_HOST_REMOTE
              value: "backend.c54o0q20gxhu.us-east-2.rds.amazonaws.com"
            - name: PGSQL_PORT_REMOTE
              value: "5432"
            - name: PGSQL_USERNAME_REMOTE
              value: "backend"
            - name: PGSQL_PASSWORD_REMOTE
              value: "6M7CLXXeiWBHYUlN"
            - name: PGSQL_DB_NAME_REMOTE
              value: "queue"
            - name: PGSQL_SSL_MODE_REMOTE
              value: "require"
            - name: PGSQL_HOST
              value: "backend.c54o0q20gxhu.us-east-2.rds.amazonaws.com"
            - name: PGSQL_PORT
              value: "5432"
            - name: PGSQL_USERNAME
              value: "backend"
            - name: PGSQL_PASSWORD
              value: "6M7CLXXeiWBHYUlN"
            - name: PGSQL_DB_NAME
              value: "rr"
            - name: MONITOR_HOST
              value: "http://monitor.apps.svc.cluster.local"
            - name: MONITOR_PORT
              value: "7795"
            - name: CHANNEL
              value: "servicebus_queue"
            - name: ADD_VALIDATION_REPORT_DATA
              value: "no"
            - name: AZURE_STORAGE_CONTAINER_NAME
              value: "splitter-dev"
            - name: AZURE_STORAGE_CONNECTION_STRING
              value: "DefaultEndpointsProtocol=https;AccountName=devatom;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
            - name: TENANT_NAME
              value: "atom"
            - name: CRYPTOGRAPHY_KEY
              value: "zqnR3gKxR2h_xZ9YqL8n7XgP6sW5tY3jK2mN1bC0vFg="