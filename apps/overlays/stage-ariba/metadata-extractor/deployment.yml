---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: metadata-extractor
  namespace: ariba
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 1
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: metadata-extractor
  template:
    metadata:
      labels:
        app: metadata-extractor
    spec:
      serviceAccountName: metadata-extractor-ariba
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - "ml-workloads"
      tolerations:
        - key: "ml-workloads"
          operator: "Equal"
          value: "true"
          effect: "NoExecute"

      volumes:
        # - name: secrets-store-inline
        #   csi:
        #     driver: secrets-store.csi.k8s.io
        #     readOnly: true
        #     volumeAttributes:
        #       secretProviderClass: metadata-extractor
        - name: models
          emptyDir: {}
      initContainers:
      - name: model-downloader
        image: amazon/aws-cli
        command:
          - "/bin/sh"
          - "-c"
          - |
            echo "Starting model and config download for metadata-extractor..."
            aws s3 cp s3://atom-advantage-ml-data-stage/handwritten.pt /models_init/handwritten.pt && \
            aws s3 cp s3://atom-advantage-ml-data-stage/lang_detector.pt /models_init/lang_detector.pt && \
            aws s3 cp s3://atom-advantage-ml-data-stage/medical_records_title_and_text_layout_model.pt /models_init/medical_records_title_and_text_layout_model.pt && \
            aws s3 cp s3://atom-advantage-ml-data-stage/rfa_expedited_checkbox_model.pt /models_init/rfa_expedited_checkbox_model.pt && \
            aws s3 cp s3://atom-advantage-ml-data-stage/stamp_is_rush_model.pt /models_init/stamp_is_rush_model.pt && \
            aws s3 cp s3://atom-advantage-ml-data-stage/work_status_summary_checkbox_model.pt /models_init/work_status_summary_checkbox_model.pt && \
            aws s3 cp s3://atom-advantage-ml-data-stage/work_status_title_and_text_layout_model.pt /models_init/work_status_title_and_text_layout_model.pt && \
            echo "Models for metadata-extractor downloaded."
        volumeMounts:
          - name: models
            mountPath: /models_init
        env:
          - name: AWS_REGION
            value: "us-east-2"

      containers:
        - name: metadata-extractor
          image: 112623991000.dkr.ecr.us-east-2.amazonaws.com/metadata-extractor:f1b870dc89002c2cb4c160539f109996040f75af
          imagePullPolicy: Always
          resources:
            limits:
              nvidia.com/gpu: 1
              memory: "12Gi" 
              cpu: "1000m"
            requests:
              nvidia.com/gpu: 1
              memory: "6Gi"
              cpu: "500m"
          # envFrom:
          #   - secretRef:
          #       name: metadata-extractor
          env:
            - name: PROJECT_NAME
              value: "metadata-extractor-project" 
            - name: APP_NAME
              value: "metadata-extractor-service"
            - name: LOGGING_LEVEL
              value: "INFO"
            - name: METADATA_EXTRACTOR_DEVICE
              value: "cuda:0" 
            - name: HANDWRITTEN_MODEL_PATH
              value: "/models/handwritten.pt"
            - name: LANG_DETECTOR_MODEL_PATH
              value: "/models/lang_detector.pt"
            - name: MEDICAL_RECORDS_LAYOUT_MODEL_PATH
              value: "/models/medical_records_title_and_text_layout_model.pt"
            - name: RFA_CHECKBOX_MODEL_PATH
              value: "/models/rfa_expedited_checkbox_model.pt"
            - name: RUSH_STAMP_MODEL_PATH
              value: "/models/stamp_is_rush_model.pt"
            - name: WORK_STATUS_CHECKBOX_MODEL_PATH
              value: "/models/work_status_summary_checkbox_model.pt"
            - name: WORK_STATUS_LAYOUT_MODEL_PATH
              value: "/models/work_status_title_and_text_layout_model.pt"
            - name: KEYWORDS_CONFIG_PATH
              value: "/app/keywords_config.yaml"
            - name: RABBITMQ_HOST
              value: rabbitmq.rabbitmq.svc.cluster.local
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME
              value: "ariba_to_extract_metadata"
            - name: RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME
              value: "ariba_to_postprocess_metadata"
            - name: RABBITMQ_USERNAME
              value: "user"
            - name: RABBITMQ_PASSWORD
              value: "1hENwBdjtrGj29Tg"
            - name: DB_PORT
              value: "5432"
            - name: MINIO_URI
              value: "minio.minio.svc.cluster.local:9000"
            - name: MINIO_ACCESS_KEY
              value: "sqMPOQPW04o3Oaagd2vb"
            - name: MINIO_SECRET_KEY
              value: "p5uCaFcPCyZY1t562oJVwUILHqTXiqSSvNBpgzgc"
            - name: MINIO_FILES_BUCKET
              value: "atom-advantage-packets-stage"
            - name: MINIO_SECURE
              value: "false"
            - name: SERVER_HOST
              value: "llm-server.ariba.svc.cluster.local"
            - name: SERVER_PORT
              value: "11434"
            - name: PGSQL_HOST
              value: "backend-ariba.cpowm4cec1q3.us-east-2.rds.amazonaws.com"
            - name: PGSQL_PORT
              value: "5432"
            - name: PGSQL_USERNAME
              value: "backend"
            - name: PGSQL_PASSWORD
              value: "fYLgCBZrc0lMrC8Q"
            - name: PGSQL_DB_NAME
              value: "rr"
          volumeMounts:
            # - name: secrets-store-inline
            #   mountPath: "/secrets" 
            #   readOnly: true
            - name: models
              mountPath: "/models" 
