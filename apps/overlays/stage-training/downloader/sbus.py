import json
from datetime import datetime, timedelta
from azure.servicebus import ServiceBusClient, ServiceBusMessage
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions

# Service Bus
SERVICEBUS_CONNECTION_STRING = "Endpoint=sb://atom-advantage-dev-service-bus-1.servicebus.windows.net/;SharedAccessKeyName=atom-dev-downloader;SharedAccessKey=GHgu5dmVwaZtng5kXS4Xd4l3xx/T4p/W8+ASbGMN3Iw=;"
QUEUE_NAME = "atom-dev-downloader"

# Azure Blob Storage
AZURE_STORAGE_CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=devatom;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
CONTAINER_NAME = "downloader-dev"
SAS_EXPIRATION_DAYS = 7

def generate_sas_url(blob_name, account_name, account_key):
    sas_token = generate_blob_sas(
        account_name=account_name,
        container_name=CONTAINER_NAME,
        blob_name=blob_name,
        account_key=account_key,
        permission=BlobSasPermissions(read=True),
        expiry=datetime.utcnow() + timedelta(days=SAS_EXPIRATION_DAYS)
    )
    return f"https://{account_name}.blob.core.windows.net/{CONTAINER_NAME}/{blob_name}?{sas_token}"

def send_message(sas_uri, blob_name):
    servicebus_client = ServiceBusClient.from_connection_string(SERVICEBUS_CONNECTION_STRING)
    with servicebus_client:
        sender = servicebus_client.get_queue_sender(queue_name=QUEUE_NAME)
        with sender:
            message_body = json.dumps({
                "sasUri": sas_uri,
                "blobName": blob_name
            })
            message = ServiceBusMessage(message_body)
            sender.send_messages(message)
            print(f"[OK] Enviado: {blob_name}")

if __name__ == "__main__":
    blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
    container_client = blob_service_client.get_container_client(CONTAINER_NAME)
    account_name = blob_service_client.account_name
    account_key = blob_service_client.credential.account_key

    blobs = container_client.list_blobs()

    for i, blob in enumerate(blobs):
        if i >= 25:
            break
        sas_url = generate_sas_url(blob.name, account_name, account_key)
        send_message(sas_url, blob.name)
