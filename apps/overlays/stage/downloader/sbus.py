from azure.servicebus import ServiceBusClient, ServiceBusMessage
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions
import json
import os
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import os
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# Service Bus configuration
CONNECTION_STR = "Endpoint=sb://atom-advantage-dev-service-bus-1.servicebus.windows.net/;SharedAccessKeyName=atom-dev-downloader;SharedAccessKey=GHgu5dmVwaZtng5kXS4Xd4l3xx/T4p/W8+ASbGMN3Iw=;EntityPath=atom-dev-downloader"
QUEUE_NAME = "atom-dev-downloader"

# Azure Blob Storage configuration - Update these with your actual values
AZURE_STORAGE_CONNECTION_STRING = os.environ.get("AZURE_STORAGE_CONNECTION_STRING", "DefaultEndpointsProtocol=https;AccountName=devatom;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net")
AZURE_CONTAINER_NAME = os.environ.get("AZURE_CONTAINER_NAME", "downloader-dev")

# Batch processing configuration
BATCH_SIZE = 100  # Number of messages to send in one batch
MAX_WORKERS = 10  # Number of concurrent threads for processing

def get_all_blobs_from_storage():
    """Get all blob names from Azure Blob Storage"""
    try:
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
        container_client = blob_service_client.get_container_client(AZURE_CONTAINER_NAME)
        
        blob_list = []
        blobs = container_client.list_blobs()
        
        for blob in blobs:
            blob_list.append(blob.name)
            
        print(f"Found {len(blob_list)} files in blob storage container '{AZURE_CONTAINER_NAME}'")
        return blob_list
        
    except Exception as e:
        print(f"Error retrieving blobs from storage: {e}")
        return []

def generate_sas_uri_for_blob(blob_name):
    """Generate SAS URI for a specific blob"""
    try:
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
        
        # Generate SAS token for the blob
        sas_token = generate_blob_sas(
            account_name=blob_service_client.account_name,
            container_name=AZURE_CONTAINER_NAME,
            blob_name=blob_name,
            account_key=blob_service_client.credential.account_key,
            permission=BlobSasPermissions(read=True),
            expiry=datetime.utcnow() + timedelta(days=7)  # 7 days expiry
        )
        
        # Construct the SAS URI
        sas_uri = f"https://{blob_service_client.account_name}.blob.core.windows.net/{AZURE_CONTAINER_NAME}/{blob_name}?{sas_token}"
        return sas_uri
        
    except Exception as e:
        print(f"Error generating SAS URI for blob '{blob_name}': {e}")
        return None

def send_messages_batch_to_servicebus(messages):
    """Send a batch of messages to Service Bus"""
    try:
        servicebus_client = ServiceBusClient.from_connection_string(
            conn_str=CONNECTION_STR,
            logging_enable=True
        )

        with servicebus_client:
            sender = servicebus_client.get_queue_sender(queue_name=QUEUE_NAME)
            with sender:
                sender.send_messages(messages)
        
        return True
        
    except Exception as e:
        print(f"Error sending batch messages to Service Bus: {e}")
        return False

def process_blob_batch(blob_names):
    """Process a batch of blobs: generate SAS URIs and create Service Bus messages"""
    messages = []
    successful_blobs = []
    
    for blob_name in blob_names:
        sas_uri = generate_sas_uri_for_blob(blob_name)
        if sas_uri:
            # Create the message content
            message_content = {
                "sasUri": sas_uri,
                "blobName": blob_name
            }
            
            # Convert to JSON string and create Service Bus message
            message_body = json.dumps(message_content)
            message = ServiceBusMessage(message_body)
            messages.append(message)
            successful_blobs.append(blob_name)
        else:
            print(f"Skipping blob '{blob_name}' due to SAS URI generation failure")
    
    return messages, successful_blobs

def process_all_blobs_concurrently():
    """Main function to process all blobs with concurrent batch processing"""
    start_time = time.time()
    
    # Get all blobs from storage
    all_blobs = get_all_blobs_from_storage()
    if not all_blobs:
        print("No blobs found or error retrieving blobs. Exiting.")
        return
    
    # Split blobs into batches
    blob_batches = [all_blobs[i:i + BATCH_SIZE] for i in range(0, len(all_blobs), BATCH_SIZE)]
    print(f"Processing {len(all_blobs)} blobs in {len(blob_batches)} batches of {BATCH_SIZE}")
    
    total_successful = 0
    total_failed = 0
    
    # Process batches concurrently
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # Submit all batch processing tasks
        future_to_batch = {
            executor.submit(process_blob_batch, batch): batch 
            for batch in blob_batches
        }
        
        # Process completed batches
        for future in as_completed(future_to_batch):
            batch = future_to_batch[future]
            try:
                messages, successful_blobs = future.result()
                
                if messages:
                    # Send the batch of messages to Service Bus
                    if send_messages_batch_to_servicebus(messages):
                        total_successful += len(successful_blobs)
                        print(f"Successfully sent {len(messages)} messages to Service Bus queue")
                    else:
                        total_failed += len(successful_blobs)
                        print(f"Failed to send batch of {len(messages)} messages")
                else:
                    total_failed += len(batch)
                    print(f"No valid messages generated for batch")
                    
            except Exception as e:
                total_failed += len(batch)
                print(f"Error processing batch: {e}")
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n=== Processing Summary ===")
    print(f"Total blobs processed: {len(all_blobs)}")
    print(f"Successfully sent: {total_successful}")
    print(f"Failed: {total_failed}")
    print(f"Total processing time: {processing_time:.2f} seconds")
    print(f"Average time per blob: {processing_time/len(all_blobs):.3f} seconds")
CONNECTION_STR = "Endpoint=sb://atom-advantage-dev-service-bus-1.servicebus.windows.net/;SharedAccessKeyName=atom-dev-downloader;SharedAccessKey=GHgu5dmVwaZtng5kXS4Xd4l3xx/T4p/W8+ASbGMN3Iw=;EntityPath=atom-dev-downloader"
QUEUE_NAME = "atom-dev-downloader"

# Azure Blob Storage configuration - Update these with your actual values
AZURE_STORAGE_CONNECTION_STRING = os.environ.get("AZURE_STORAGE_CONNECTION_STRING", "DefaultEndpointsProtocol=https;AccountName=devatom;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net")
AZURE_CONTAINER_NAME = os.environ.get("AZURE_CONTAINER_NAME", "downloader-dev")

# Batch processing configuration
BATCH_SIZE = 100  # Number of messages to send in one batch
MAX_WORKERS = 10  # Number of concurrent threads for processing

def get_all_blobs_from_storage():
    """Get all blob names from Azure Blob Storage"""
    try:
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
        container_client = blob_service_client.get_container_client(AZURE_CONTAINER_NAME)
        
        blob_list = []
        blobs = container_client.list_blobs()
        
        for blob in blobs:
            blob_list.append(blob.name)
            
        print(f"Found {len(blob_list)} files in blob storage container '{AZURE_CONTAINER_NAME}'")
        return blob_list
        
    except Exception as e:
        print(f"Error retrieving blobs from storage: {e}")
        return []

def generate_sas_uri_for_blob(blob_name):
    """Generate SAS URI for a specific blob"""
    try:
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
        
        # Generate SAS token for the blob
        sas_token = generate_blob_sas(
            account_name=blob_service_client.account_name,
            container_name=AZURE_CONTAINER_NAME,
            blob_name=blob_name,
            account_key=blob_service_client.credential.account_key,
            permission=BlobSasPermissions(read=True),
            expiry=datetime.utcnow() + timedelta(days=7)  # 7 days expiry
        )
        
        # Construct the SAS URI
        sas_uri = f"https://{blob_service_client.account_name}.blob.core.windows.net/{AZURE_CONTAINER_NAME}/{blob_name}?{sas_token}"
        return sas_uri
        
    except Exception as e:
        print(f"Error generating SAS URI for blob '{blob_name}': {e}")
        return None

def send_messages_batch_to_servicebus(messages):
    """Send a batch of messages to Service Bus"""
    try:
        servicebus_client = ServiceBusClient.from_connection_string(
            conn_str=CONNECTION_STR,
            logging_enable=True
        )

        with servicebus_client:
            sender = servicebus_client.get_queue_sender(queue_name=QUEUE_NAME)
            with sender:
                sender.send_messages(messages)
        
        return True
        
    except Exception as e:
        print(f"Error sending batch messages to Service Bus: {e}")
        return False

def process_blob_batch(blob_names):
    """Process a batch of blobs: generate SAS URIs and create Service Bus messages"""
    messages = []
    successful_blobs = []
    
    for blob_name in blob_names:
        sas_uri = generate_sas_uri_for_blob(blob_name)
        if sas_uri:
            # Create the message content
            message_content = {
                "sasUri": sas_uri,
                "blobName": blob_name,
                "tenant_id": "atomdevid",  # Replace with actual tenant ID
                "subtenant_id": "atomdevtestid"  # Replace with actual subtenant ID
            }
            
            # Convert to JSON string and create Service Bus message
            message_body = json.dumps(message_content)
            message = ServiceBusMessage(message_body)
            messages.append(message)
            successful_blobs.append(blob_name)
        else:
            print(f"Skipping blob '{blob_name}' due to SAS URI generation failure")
    
    return messages, successful_blobs

def process_all_blobs_concurrently():
    """Main function to process all blobs with concurrent batch processing"""
    start_time = time.time()
    
    # Get all blobs from storage
    all_blobs = get_all_blobs_from_storage()
    if not all_blobs:
        print("No blobs found or error retrieving blobs. Exiting.")
        return
    
    # Split blobs into batches
    blob_batches = [all_blobs[i:i + BATCH_SIZE] for i in range(0, len(all_blobs), BATCH_SIZE)]
    print(f"Processing {len(all_blobs)} blobs in {len(blob_batches)} batches of {BATCH_SIZE}")
    
    total_successful = 0
    total_failed = 0
    
    # Process batches concurrently
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # Submit all batch processing tasks
        future_to_batch = {
            executor.submit(process_blob_batch, batch): batch 
            for batch in blob_batches
        }
        
        # Process completed batches
        for future in as_completed(future_to_batch):
            batch = future_to_batch[future]
            try:
                messages, successful_blobs = future.result()
                
                if messages:
                    # Send the batch of messages to Service Bus
                    if send_messages_batch_to_servicebus(messages):
                        total_successful += len(successful_blobs)
                        print(f"Successfully sent {len(messages)} messages to Service Bus queue")
                    else:
                        total_failed += len(successful_blobs)
                        print(f"Failed to send batch of {len(messages)} messages")
                else:
                    total_failed += len(batch)
                    print(f"No valid messages generated for batch")
                    
            except Exception as e:
                total_failed += len(batch)
                print(f"Error processing batch: {e}")
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n=== Processing Summary ===")
    print(f"Total blobs processed: {len(all_blobs)}")
    print(f"Successfully sent: {total_successful}")
    print(f"Failed: {total_failed}")
    print(f"Total processing time: {processing_time:.2f} seconds")
    print(f"Average time per blob: {processing_time/len(all_blobs):.3f} seconds")

def send_message_to_servicebus(sas_uri, blob_name):
    """Legacy function for single message sending (kept for backward compatibility)"""
    """Legacy function for single message sending (kept for backward compatibility)"""
    try:
        servicebus_client = ServiceBusClient.from_connection_string(
            conn_str=CONNECTION_STR,
            logging_enable=True
        )

        message_content = {
            "sasUri": sas_uri,
            "blobName": blob_name
        }

        message_body = json.dumps(message_content)
        message = ServiceBusMessage(message_body)

        with servicebus_client:
            sender = servicebus_client.get_queue_sender(queue_name=QUEUE_NAME)
            with sender:
                sender.send_messages(message)
            print(f"Message sent to queue {QUEUE_NAME}")

    except Exception as e:
        print(f"Error sending message to Service Bus: {e}")

if __name__ == "__main__":
    # Process all blobs from storage and send messages
    process_all_blobs_concurrently()
