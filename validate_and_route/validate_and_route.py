import json
import os
import re
import sys
from datetime import timed<PERSON><PERSON>
from io import BytesIO
from statistics import mean
import os
import time
from uuid6 import uuid7
import pika
import ssl
import psycopg2
import urllib3
import yaml
from minio import Minio
from minio.error import S3Error
from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.orm.attributes import flag_modified
from cryptography.fernet import Fernet
import ast
from pipeline_utils.rabbitmq_connector import PikaServiceFactory
from pipeline_utils.database_connector import DBConnector
from pipeline_utils.tenant_utils import get_tenant_processing_config, default_tenant_config, get_tenant_aware_queue_name

from split_doc_to_chunks import large_file_handling

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from models import Document, IncomingPackage, Splitted_Document, DocumentChunk
from pipeline_utils.monitoring import MonitorService, Status

# configuration = yaml.safe_load(open("config.yml"))

PGSQL_HOST = os.environ.get("PGSQL_HOST")
PGSQL_PORT = int(os.environ.get("PGSQL_PORT"))
PGSQL_USERNAME = os.environ.get("PGSQL_USERNAME")
PGSQL_PASSWORD = os.environ.get("PGSQL_PASSWORD")
PGSQL_DB_NAME = os.environ.get("PGSQL_DB_NAME")

RABBITMQ_HOST = os.environ.get("RABBITMQ_HOST")
RABBITMQ_PORT = int(os.environ.get("RABBITMQ_PORT"))
RABBITMQ_USERNAME = os.environ.get("RABBITMQ_USERNAME")
RABBITMQ_PASSWORD = os.environ.get("RABBITMQ_PASSWORD")
RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME = os.environ.get("RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME")
RABBITMQ_TO_VALIDATE_QUEUE_NAME = os.environ.get("RABBITMQ_TO_VALIDATE_QUEUE_NAME")
RABBITMQ_TO_UPLOAD_QUEUE_NAME = os.environ.get("RABBITMQ_TO_UPLOAD_QUEUE_NAME")
RABBITMQ_TO_BACKEND_QA_QUEUE_NAME = os.environ.get("RABBITMQ_TO_BACKEND_QA_QUEUE_NAME")
RABBITMQ_TO_BACKEND_OVER_QA_NAME = os.environ.get("RABBITMQ_TO_BACKEND_OVER_QA_NAME")
# REMOTE_RABBITMQ_HOST = os.environ.get("REMOTE_RABBITMQ_HOST")
# REMOTE_RABBITMQ_PORT = os.environ.get("REMOTE_RABBITMQ_PORT")
# REMOTE_RABBITMQ_VHOST = os.environ.get("REMOTE_RABBITMQ_VHOST")
# REMOTE_RABBITMQ_USERNAME = os.environ.get("REMOTE_RABBITMQ_USERNAME")
# REMOTE_RABBITMQ_PASSWORD = os.environ.get("REMOTE_RABBITMQ_PASSWORD")


MINIO_URI = os.environ.get("MINIO_URI")
MINIO_ACCESS_KEY = os.environ.get("MINIO_ACCESS_KEY")
MINIO_SECRET_KEY = os.environ.get("MINIO_SECRET_KEY")
MINIO_FILES_BUCKET = os.environ.get("MINIO_FILES_BUCKET")
MINIO_OBJECT_URI_PREFIX = os.environ.get("MINIO_OBJECT_URI_PREFIX")
MINIO_SECURE = os.environ.get("MINIO_SECURE").lower() == "true"
MINIO_EXPIRATION_TIME = int(os.environ.get("MINIO_EXPIRATION_TIME"))
MINIO_PRESIGNED_URL_HOST = os.environ.get("MINIO_PRESIGNED_URL_HOST")
MINIO_PRESIGNED_URL_SECURE = os.environ.get("MINIO_PRESIGNED_URL_SECURE").lower() == "true"

MINIMUM_SIZE_OF_LARGE_FILE = int(os.environ.get("MINIMUM_SIZE_OF_LARGE_FILE"))
MINIMAL_CLASSIFICATION_CONFIDENCE = float(os.environ.get("MINIMAL_CLASSIFICATION_CONFIDENCE"))
MINIMAL_METADATA_CONFIDENCE = float(os.environ.get("MINIMAL_METADATA_CONFIDENCE"))
FORCE_ROUTE = int(os.environ.get("FORCE_ROUTE"))
metadata_display_str = os.environ.get("METADATA_DISPLAY", "[]")
METADATA_DISPLAY = []

try:
    parsed_list = ast.literal_eval(metadata_display_str)
    if isinstance(parsed_list, list):
        METADATA_DISPLAY = parsed_list
    else:
        print(f"Warning: METADATA_DISPLAY was not a list after parsing: {metadata_display_str}")
        METADATA_DISPLAY = ['namingData', 'metadata']
except (ValueError, SyntaxError) as e:
    print(f"Warning: Could not parse METADATA_DISPLAY: {metadata_display_str}. Error: {e}. Using default.")
    METADATA_DISPLAY = ['namingData', 'metadata']

API_HOST = os.environ.get("MONITOR_HOST")
API_PORT = os.environ.get("MONITOR_PORT")
PROJECT_NAME = os.environ.get("PROJECT_NAME")
APP_NAME = os.environ.get("APP_NAME")

ENCRYPTION_KEY = os.environ.get("CRYPTOGRAPHY_KEY")
TENANT_NAME = os.environ.get("TENANT_NAME")
# ENCRYPTION_KEY = Fernet.generate_key()

Base = declarative_base()

db_connector = DBConnector(
    pgsql_host=PGSQL_HOST,
    pgsql_port=PGSQL_PORT,
    pgsql_username=PGSQL_USERNAME,
    pgsql_password=PGSQL_PASSWORD,
    pgsql_db_name=PGSQL_DB_NAME
)
Base.metadata.create_all(db_connector.get_engine())
session = None

httpClient = urllib3.PoolManager()

REMOTE_SSL_CAFILE_PATH  = os.environ.get('REMOTE_SSL_CAFILE_PATH')
REMOTE_SSL_CERTFILE_PATH = os.environ.get('REMOTE_SSL_CERTFILE_PATH')
REMOTE_SSL_KEYFILE_PATH  = os.environ.get('REMOTE_SSL_KEYFILE_PATH')

context = None
if REMOTE_SSL_CAFILE_PATH and REMOTE_SSL_CERTFILE_PATH and REMOTE_SSL_KEYFILE_PATH:
    try:
        context = ssl.create_default_context(cafile=REMOTE_SSL_CAFILE_PATH)
        context.load_cert_chain(certfile=REMOTE_SSL_CERTFILE_PATH,
                                keyfile=REMOTE_SSL_KEYFILE_PATH)
        print("SSL context for RabbitMQ configured successfully.")
    except Exception as e:
        print(f"Error configuring SSL context for RabbitMQ: {e}")
        context = None
else:
    print("SSL configuration not found in config. Proceeding without SSL for RabbitMQ.")

# **THIS is the important line** – only wrap the context when it exists
ssl_options = pika.SSLOptions(context) if context else None

minio_client = Minio(
    MINIO_PRESIGNED_URL_HOST,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=MINIO_PRESIGNED_URL_SECURE,
)

rmq_service_factory = PikaServiceFactory(
    host=RABBITMQ_HOST,
    port=RABBITMQ_PORT,
    username=RABBITMQ_USERNAME,
    password=RABBITMQ_PASSWORD,
    ssl_options=None,
)
rmq_service = rmq_service_factory.create_service()
rmq_service.start()

VALID_STATES = [
    "AL",
    "AK",
    "AZ",
    "AR",
    "CA",
    "CO",
    "CT",
    "DE",
    "FL",
    "GA",
    "HI",
    "ID",
    "IL",
    "IN",
    "IA",
    "KS",
    "KY",
    "LA",
    "ME",
    "MD",
    "MA",
    "MI",
    "MN",
    "MS",
    "MO",
    "MT",
    "NE",
    "NV",
    "NH",
    "NJ",
    "NM",
    "NY",
    "NC",
    "ND",
    "OH",
    "OK",
    "OR",
    "PA",
    "RI",
    "SC",
    "SD",
    "TN",
    "TX",
    "UT",
    "VT",
    "VA",
    "WA",
    "WV",
    "WI",
    "WY",
]

VALID_ABBREVIATIONS = ["Jr", "Sr", "Jr.", "Sr.", "I", "II", "III", "IV", "V"]


def get_claim_number_confidence(claim_number):
    if not claim_number:
        return 0.0
    if 6 <= len(claim_number) <= 20:
        if claim_number.endswith("001") or claim_number.endswith("1AC"):
            return 1.0
        else:
            return 0.99
    else:
        return 0.8


def get_patient_name_confidence(patient_name):
    if not patient_name:
        return 0.0

    if len(patient_name) < 6 or len(patient_name) > 30:
        return 0.8

    num_words = len(patient_name.split())

    if num_words < 2 or num_words > 4:
        return 0.8

    words = patient_name.split()

    if any(word in VALID_ABBREVIATIONS for word in words):
        return 0.8

    if re.search(r"[^A-Za-z\- ]", patient_name):
        return 0.8

    if re.match(r"^[^A-Za-z]+$", patient_name):
        return 0.5

    return 1.0


def validate_checkbox(value):
    if not value or not isinstance(value, str):
        return False
    #     return value in [True, False, ""]
    return value in ["Y", "N", None]


def validate_zip_code(value):
    if not value or not isinstance(value, str):
        return False
    pattern = r"^\d{5}(-\d{4})?$"
    return bool(re.match(pattern, value.strip()))


def validate_email(value):
    if not value or not isinstance(value, str):
        return False
    pattern = r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"
    return bool(re.match(pattern, value.strip()))


def validate_phone_number(value):
    if not value or not isinstance(value, str):
        return False
    pattern = r"^\+\d{11}$"
    return bool(re.match(pattern, value.strip()))


def validate_claim_number(value):
    if not value or not isinstance(value, str):
        return False
    if len(value) < 6 or len(value) > 16:
        return False
    if re.search(r"[@#<>{}\[\]!@$%^&*]", value):
        return False
    if "-001" in value or "-1AC" in value:
        return True
    return 6 <= len(value) <= 16


def validate_patient_name(value):
    if not value or not isinstance(value, str):
        return False
    if len(value) < 6 or len(value) > 30:
        return False
    if re.search(r"[@#<>{}\[\]!@$%^&*]", value):
        return False
    if len(value.split()) == 1:
        return False
    if re.search(r"^[\d@#<>{}\[\]!@$%^&*]+$", value):
        return False
    return True


def validate_document_date(value):
    if value is None:
        return False
    if not isinstance(value, str) or len(value) < 6 or len(value) > 15:
        return False
    if re.search(r"[@#<>{}\[\]!@$%^&*]", value):
        return False
    if not re.search(r"^[\d\s-]+$", value) or not re.search(
        r"\d{4}[\s-]?\d{2}[\s-]?\d{2}", value
    ):
        return False
    return True


def validate_document_received_date(value):
    if value is None:
        return False
    if not isinstance(value, str) or len(value) < 6 or len(value) > 15:
        return False
    if re.search(r"[@#<>{}\[\]!@$%^&*]", value):
        return False
    if not re.search(r"^[\d\s-]+$", value) or not re.search(
        r"\d{4}[\s-]?\d{2}[\s-]?\d{2}", value
    ):
        return False
    return True


def validate_date(value):
    if not value or not isinstance(value, str):
        return False
    if len(value) < 6 or len(value) > 15:
        return False
    if re.search(r"[@#<>{}\[\]!@$%^&*]", value):
        return False
    if not re.search(r"^[\d\s-]+$", value) or not re.search(
        r"\d{4}[\s-]?\d{2}[\s-]?\d{2}", value
    ):
        return False
    return True


def validate_sender_name(value):
    if not value or not isinstance(value, str):
        return False
    if re.search(r"[@#<>{}\[\]!@$%^&*]", value):
        return False
    return value.strip() != ""


def validate_state(value):
    if not value or not isinstance(value, str):
        return False
    if value not in VALID_STATES:
        return False
    return True


def validate_company(value):
    if not value or not isinstance(value, str):
        return False
    if re.search(r"[@#<>{}\[\]!@$%^&*]", value):
        return False
    return value.strip() != ""


def validate_phone_number(value):
    if not value or not isinstance(value, str):
        return False
    if re.search(r"^[+]?[0-9 ()-]+$", value):
        return True
    return False


def validate_first_name(value):
    if not value or not isinstance(value, str):
        return False
    if re.search(r"[@#<>{}\[\]!@$%^&*]", value):
        return False
    return value.strip() != ""


def validate_middle_name(value):
    if not value or not isinstance(value, str):
        return False
    if re.search(r"[@#<>{}\[\]!@$%^&*]", value):
        return False
    return value.strip() != ""


def validate_last_name(value):
    if not value or not isinstance(value, str):
        return False
    if re.search(r"[@#<>{}\[\]!@$%^&*]", value):
        return False
    return value.strip() != ""


def extract_value(validating_field, dictionary):
    current_level = dictionary
    try:
        for key in validating_field:
            current_level = current_level[key]
        return current_level
    except KeyError:
        return None
    except TypeError:
        return None


def check_keys(validating_fields, metadata):
    report = {}
    is_valid = True

    # Add mandatory validation fields
    validating_fields.extend(
        [
            {
                "validating": ["metaData", "isEnglishLanguage"],
                "validator": validate_checkbox,
            },
            {
                "validating": ["metaData", "isHandwritten"],
                "validator": validate_checkbox,
            },
        ]
    )

    # Iterate through and validate each field
    for field in validating_fields:
        print(f"\nvalidating key: {field['validating']}")

        value = extract_value(field["validating"], metadata)

        if value and "value" in value:
            if field["validator"](value["value"]):
                print(
                    f"VALID naming key value: {field['validating'][-1]}={value['value']}"
                )
                report[f"{field['validating']}"] = {
                    "value": value["value"],
                    "valid": True,
                }
            else:
                is_valid = False
                print(
                    f"INVALID naming key value: {field['validating'][-1]}={value['value']}"
                )
                report[f"{field['validating']}"] = {
                    "value": value["value"],
                    "valid": False,
                }
        else:
            is_valid = False
            print(f"INVALID naming key value: {field['validating'][-1]}={value}")
            report[f"{field['validating']}"] = {"value": None, "valid": False}

    # Fields to confidence calculation according to requirements
    calculating_confidence_fields = [
        {
            "validating": ["namingData", "claimNumber"],
            "validator": get_claim_number_confidence,
        },
        {
            "validating": ["namingData", "patientFirstName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "patientLastName"],
            "validator": get_patient_name_confidence,
        },
    ]

    confidences = []

    # Iterate through and extract confidence for each field
    for field in calculating_confidence_fields:
        print(f"\ncalculating confidence for: {field['validating']}")
        value = extract_value(field["validating"], metadata)

        if value and "value" in value:
            confidence = field["validator"](value["value"])
        else:
            confidence = 0.0
        print(
            f"key {field['validating'][-1]} has value {value['value'] if value else value} with confidence {confidence}. Minimal metadata conf is: {MINIMAL_METADATA_CONFIDENCE}"
        )

        if confidence < MINIMAL_METADATA_CONFIDENCE:
            is_valid = False
            report[f"{field['validating'][-1]}_confidence"] = {
                "confidence": confidence,
                "valid": False,
                "value": value["value"] if value and "value" in value else value,
            }
        else:
            report[f"{field['validating'][-1]}_confidence"] = {
                "confidence": confidence,
                "valid": True,
                "value": value["value"] if value and "value" in value else value,
            }
        confidences.append(confidence)

    return is_valid, mean(confidences), report


def validate_other(splitted_document):
    metadata_ml = splitted_document.metadata_ml

    validating_fields = [
        {
            "validating": ["namingData", "claimNumber"],
            "validator": validate_claim_number,
        },
        {
            "validating": ["namingData", "patientFirstName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "patientLastName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "docReceivedDate"],
            "validator": validate_document_received_date,
        },
    ]

    is_valid, conf, report = check_keys(validating_fields, metadata_ml)
    return is_valid, conf, report


def validate_rfa(splitted_document):
    metadata_ml = splitted_document.metadata_ml

    validating_fields = [
        {
            "validating": ["namingData", "claimNumber"],
            "validator": validate_claim_number,
        },
        {"validating": ["namingData", "docDate"], "validator": validate_document_date},
        {
            "validating": ["namingData", "docReceivedDate"],
            "validator": validate_document_received_date,
        },
        {"validating": ["namingData", "senderName"], "validator": validate_sender_name},
        {
            "validating": ["namingData", "patientFirstName"],
            "validator": validate_first_name,
        },
        {
            "validating": ["namingData", "patientLastName"],
            "validator": validate_last_name,
        },
        {"validating": ["namingData", "docState"], "validator": validate_state},
        {
            "validating": ["metaData", "adjuster", "company"],
            "validator": validate_company,
        },
        {
            "validating": ["metaData", "claimant", "phoneNumber"],
            "validator": validate_phone_number,
        },
        {
            "validating": ["metaData", "physician", "firstName"],
            "validator": validate_first_name,
        },
        {
            "validating": ["metaData", "physician", "lastName"],
            "validator": validate_last_name,
        },
        {"validating": ["metaData", "rushFlag"], "validator": validate_checkbox},
        {"validating": ["metaData", "newSubmission"], "validator": validate_checkbox},
        {"validating": ["metaData", "resubmission"], "validator": validate_checkbox},
        {
            "validating": ["metaData", "writtenConfirmPriorOralRequest"],
            "validator": validate_checkbox,
        },
        {"validating": ["metaData", "expeditedFlag"], "validator": validate_checkbox},
        {"validating": ["metaData", "docDate"], "validator": validate_document_date},
        {
            "validating": ["metaData", "docReceivedDate"],
            "validator": validate_document_date,
        },
        {
            "validating": ["metaData", "claim", "dateOfInjuryFrom"],
            "validator": validate_date,
        },
        {
            "validating": ["metaData", "claim", "dateOfInjuryThrough"],
            "validator": validate_date,
        },
        {
            "validating": ["metaData", "claim", "jurisState"],
            "validator": validate_state,
        },
        {
            "validating": ["metaData", "claimant", "dateOfBirth"],
            "validator": validate_date,
        },
        {
            "validating": ["metaData", "claimant", "emailAddress"],
            "validator": validate_email,
        },
        {
            "validating": ["metaData", "claimant", "address", "zip"],
            "validator": validate_zip_code,
        },
    ]

    is_valid, conf, report = check_keys(validating_fields, metadata_ml)
    return is_valid, conf, report


def validate_determination_med_auth(splitted_document):
    metadata_ml = splitted_document.metadata_ml

    validating_fields = [
        {
            "validating": ["namingData", "claimNumber"],
            "validator": validate_claim_number,
        },
        {
            "validating": ["namingData", "patientFirstName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "patientLastName"],
            "validator": get_patient_name_confidence,
        },
        {"validating": ["namingData", "docDate"], "validator": validate_document_date},
        {
            "validating": ["namingData", "docReceivedDate"],
            "validator": validate_document_received_date,
        },
    ]

    is_valid, conf, report = check_keys(validating_fields, metadata_ml)
    return is_valid, conf, report


def validate_case_management_notes(splitted_document):
    metadata_ml = splitted_document.metadata_ml

    validating_fields = [
        {
            "validating": ["namingData", "claimNumber"],
            "validator": validate_claim_number,
        },
        {
            "validating": ["namingData", "patientFirstName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "patientLastName"],
            "validator": get_patient_name_confidence,
        },
        {"validating": ["namingData", "docDate"], "validator": validate_document_date},
        {
            "validating": ["namingData", "docReceivedDate"],
            "validator": validate_document_received_date,
        },
    ]

    is_valid, conf, report = check_keys(validating_fields, metadata_ml)
    return is_valid, conf, report


def validate_medical_records(splitted_document):
    metadata_ml = splitted_document.metadata_ml

    validating_fields = [
        {
            "validating": ["namingData", "claimNumber"],
            "validator": validate_claim_number,
        },
        {
            "validating": ["namingData", "patientFirstName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "patientLastName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "docReceivedDate"],
            "validator": validate_document_received_date,
        },
    ]

    is_valid, conf, report = check_keys(validating_fields, metadata_ml)
    return is_valid, conf, report


def validate_fax(splitted_document):
    metadata_ml = splitted_document.metadata_ml

    validating_fields = [
        {
            "validating": ["namingData", "claimNumber"],
            "validator": validate_claim_number,
        },
        {
            "validating": ["namingData", "patientFirstName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "patientLastName"],
            "validator": get_patient_name_confidence,
        },
        {"validating": ["namingData", "senderName"], "validator": validate_sender_name},
        {
            "validating": ["namingData", "docReceivedDate"],
            "validator": validate_document_received_date,
        },
    ]

    is_valid, conf, report = check_keys(validating_fields, metadata_ml)
    return is_valid, conf, report


def validate_imr_ime_qme(splitted_document):
    metadata_ml = splitted_document.metadata_ml

    validating_fields = [
        {
            "validating": ["namingData", "claimNumber"],
            "validator": validate_claim_number,
        },
        {
            "validating": ["namingData", "patientFirstName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "patientLastName"],
            "validator": get_patient_name_confidence,
        },
        {"validating": ["namingData", "docDate"], "validator": validate_document_date},
        {
            "validating": ["namingData", "docReceivedDate"],
            "validator": validate_document_received_date,
        },
    ]

    is_valid, conf, report = check_keys(validating_fields, metadata_ml)
    return is_valid, conf, report


def validate_illness_injury_report_froi(splitted_document):
    metadata_ml = splitted_document.metadata_ml

    validating_fields = [
        {
            "validating": ["namingData", "claimNumber"],
            "validator": validate_claim_number,
        },
        {
            "validating": ["namingData", "patientFirstName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "patientLastName"],
            "validator": get_patient_name_confidence,
        },
        {"validating": ["namingData", "docDate"], "validator": validate_document_date},
        {
            "validating": ["namingData", "docReceivedDate"],
            "validator": validate_document_received_date,
        },
    ]

    is_valid, conf, report = check_keys(validating_fields, metadata_ml)
    return is_valid, conf, report


def validate_supplemental_work_status(splitted_document):
    metadata_ml = splitted_document.metadata_ml

    validating_fields = [
        {
            "validating": ["namingData", "claimNumber"],
            "validator": validate_claim_number,
        },
        {
            "validating": ["namingData", "patientFirstName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "patientLastName"],
            "validator": get_patient_name_confidence,
        },
        {"validating": ["namingData", "docDate"], "validator": validate_document_date},
        {
            "validating": ["namingData", "docReceivedDate"],
            "validator": validate_document_received_date,
        },
    ]

    is_valid, conf, report = check_keys(validating_fields, metadata_ml)
    return is_valid, conf, report


def validate_misc_correspondence(splitted_document):
    metadata_ml = splitted_document.metadata_ml

    validating_fields = [
        {
            "validating": ["namingData", "claimNumber"],
            "validator": validate_claim_number,
        },
        {
            "validating": ["namingData", "patientFirstName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "patientLastName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "docReceivedDate"],
            "validator": validate_document_received_date,
        },
    ]

    is_valid, conf, report = check_keys(validating_fields, metadata_ml)
    return is_valid, conf, report

def validate_hospital_bill_ub(splitted_document):
    metadata_ml = splitted_document.metadata_ml

    validating_fields = [
        {
            "validating": ["namingData", "claimNumber"],
            "validator": validate_claim_number,
        },
        {
            "validating": ["namingData", "patientFirstName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "patientLastName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "docReceivedDate"],
            "validator": validate_document_received_date,
        },
    ]

    is_valid, conf, report = check_keys(validating_fields, metadata_ml)
    return is_valid, conf, report

def validate_physician_bill_hcfa(splitted_document):
    metadata_ml = splitted_document.metadata_ml

    validating_fields = [
        {
            "validating": ["namingData", "claimNumber"],
            "validator": validate_claim_number,
        },
        {
            "validating": ["namingData", "patientFirstName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "patientLastName"],
            "validator": get_patient_name_confidence,
        },
        {
            "validating": ["namingData", "docReceivedDate"],
            "validator": validate_document_received_date,
        },
    ]

    is_valid, conf, report = check_keys(validating_fields, metadata_ml)
    return is_valid, conf, report


def get_tenant_validation_config(tenant_id=None, subtenant_id=None):
    """
    Get tenant-specific validation configuration.
    
    Args:
        tenant_id: Tenant ID
        subtenant_id: Subtenant ID
    
    Returns:
        Dictionary containing validation configuration
    """
    if not tenant_id:
        return {
            'minimum_classification_confidence': MINIMAL_CLASSIFICATION_CONFIDENCE,
            'minimum_metadata_confidence': MINIMAL_METADATA_CONFIDENCE,
            'force_qa_review': False,
            'large_file_threshold': MINIMUM_SIZE_OF_LARGE_FILE,
            'custom_validation_rules': {}
        }
    
    try:
        processing_config = get_tenant_processing_config(tenant_id, subtenant_id)
        tenant_config = default_tenant_config.get_tenant_config(tenant_id, subtenant_id)
        
        return {
            'minimum_classification_confidence': processing_config.get('minimum_classification_confidence', MINIMAL_CLASSIFICATION_CONFIDENCE),
            'minimum_metadata_confidence': processing_config.get('minimum_metadata_confidence', MINIMAL_METADATA_CONFIDENCE),
            'force_qa_review': processing_config.get('force_qa_review', False),
            'large_file_threshold': tenant_config.get('storage_settings', {}).get('large_file_threshold', MINIMUM_SIZE_OF_LARGE_FILE),
            'custom_validation_rules': tenant_config.get('document_types', {}).get('custom_validation_rules', {})
        }
    except Exception as e:
        print(f"Error getting tenant validation config: {e}")
        return {
            'minimum_classification_confidence': MINIMAL_CLASSIFICATION_CONFIDENCE,
            'minimum_metadata_confidence': MINIMAL_METADATA_CONFIDENCE,
            'force_qa_review': False,
            'large_file_threshold': MINIMUM_SIZE_OF_LARGE_FILE,
            'custom_validation_rules': {}
        }


def validate_each(splitted_documents, tenant_id=None, subtenant_id=None):
    """
    Validates a list of splitted documents according to their document types and tenant-specific rules.

    Args:
        splitted_documents (list): A list of splitted documents to validate.
        tenant_id: Tenant ID for tenant-specific validation
        subtenant_id: Subtenant ID for tenant-specific validation

    Returns:
        tuple: A tuple containing:
            - is_valid (int): 1 if all documents are valid, 0 otherwise.
            - validation_report (dict): A dictionary containing validation details for each document.

    Steps:
        1. Get tenant-specific validation configuration.
        2. Initialize the validation report with metadata including tenant information.
        3. For each splitted document:
            a. Call the 'validate' function to check if the document is valid.
            b. Update the document's overall confidence in the database.
            c. Add the validation result to the validation report.
        4. Check if all documents in the list are valid:
            a. If all documents are valid, return 1.
            b. If any document is invalid, return 0.
        5. Return the validation status and the validation report.

    Note:
        The function relies on the 'validate' function to perform the actual validation of individual documents.
        The validation report includes details about each document's validation status and confidence.
    """
    # Get tenant-specific validation configuration
    validation_config = get_tenant_validation_config(tenant_id, subtenant_id)
    
    print(f"Using tenant-specific validation config for tenant {tenant_id}: {validation_config}")

    validation_report = {}
    is_valid = 1
    
    for index, splitted_document in enumerate(splitted_documents):
        print(f"\n\nvalidating: {splitted_document.document_type}")
        print(
            f"incoming package name: {splitted_document.parent_document.incoming_package.original_name}"
        )
        
        # Apply tenant-specific validation
        document_is_valid, document_conf, document_report = validate(splitted_document, validation_config)
        
        # Calculate overall confidence as mean of classification and metadata confidence
        overall_confidence = mean([splitted_document.classification_confidence, document_conf])
        document_report["overall_confidence"] = overall_confidence
        document_report["pages"] = splitted_document.parent_document_pages

        validation_report[
            f"{splitted_document.uuid}_{splitted_document.document_type}"
        ] = document_report

        update_doc_conf(splitted_document, overall_confidence, index)
        
        if not document_is_valid:
            is_valid = 0
    
    print(f"Validation complete. Overall is_valid: {is_valid}")
    return is_valid, validation_report


def validate(splitted_document, validation_config=None):
    """
    Validates a splitted document based on its document type and tenant-specific configuration.

    Args:
        splitted_document: The splitted document object to validate.
        validation_config: Tenant-specific validation configuration.

    Returns:
        tuple: (is_valid, overall_confidence, report)
            - is_valid (bool): True if the document is valid, False otherwise.
            - overall_confidence (float): The overall confidence score for the document.
            - report (dict): Detailed validation report.

    Steps:
        1. Extract the document type from the splitted document.
        2. Apply tenant-specific validation thresholds.
        3. Based on the document type, call the appropriate validation function.
        4. Build comprehensive validation report including classification confidence.
        5. Return the validation result, overall confidence, and report.

    Note:
        This function acts as a dispatcher, calling the appropriate validation function based on the document type.
        Each specific validation function implements the validation logic for that particular document type.
    """
    if validation_config is None:
        validation_config = get_tenant_validation_config()
    
    document_type = splitted_document.document_type
    
    # Apply tenant-specific confidence thresholds during validation
    min_classification_confidence = validation_config['minimum_classification_confidence']
    min_metadata_confidence = validation_config['minimum_metadata_confidence']
    
    # Use original validator dictionary approach
    validators = {
        "other": validate_other,
        "rfa": validate_rfa,
        "determination - med auth": validate_determination_med_auth,
        "case management notes": validate_case_management_notes,
        "medical records": validate_medical_records,
        "hospital bill (ub)": validate_hospital_bill_ub,
        "physician bill (hcfa)": validate_physician_bill_hcfa,
        "fax": validate_fax,
        "imr/ime/qme": validate_imr_ime_qme,
        "injury/illness/froi": validate_illness_injury_report_froi,
        "supplemental/work status": validate_supplemental_work_status,
        "misc correspondence": validate_misc_correspondence,
    }
    
    is_valid = False
    conf = 0.0
    report = {}
    
    if splitted_document.metadata_ml:
        is_valid, conf, report = validators[document_type.lower()](splitted_document)

    print(
        f"\ndocument classification confidence is: {splitted_document.classification_confidence}\n"
    )
    
    # Apply tenant-specific classification confidence threshold
    if splitted_document.classification_confidence < min_classification_confidence:
        print(f"document classification confidence is less than minimal value: {splitted_document.classification_confidence} < {min_classification_confidence}")
        is_valid = False
        report["classification_confidence"] = {
            "value": splitted_document.classification_confidence,
            "valid": False,
        }
    else:
        report["classification_confidence"] = {
            "value": splitted_document.classification_confidence,
            "valid": True,
        }
    
    if document_type == "Other":
        is_valid = False

    return is_valid, conf, report


def get_parent_document(parent_document_uuid):
    global session
    document = session.query(Document).filter_by(uuid=parent_document_uuid).first()
    return document


def get_splitted_document(splitted_document_uuid):
    global session
    document = (
        session.query(Splitted_Document).filter_by(uuid=splitted_document_uuid).first()
    )
    return document


def all_splitted_docs_are_ready_to_validation(incoming_package, splitted_document_uuid):
    # target IncomingPackage UUID
    target_uuid = incoming_package.uuid

    # Query to find all Splitted_Document items related to the IncomingPackage with the given UUID
    splitted_documents = (
        session.query(Splitted_Document)
        .join(Document, Document.uuid == Splitted_Document.parent_document_uuid)
        .join(IncomingPackage, IncomingPackage.uuid == Document.incoming_package_uuid)
        .filter(IncomingPackage.uuid == target_uuid)
        .all()
    )

    for doc in splitted_documents:
        if doc.uuid != splitted_document_uuid:
            if doc.status != "to_validate":
                return False, None
    sorted_splitted_documents = sorted(
        splitted_documents,
        key=lambda x: min(page[0] for page in x.parent_document_pages),
    )
    return True, sorted_splitted_documents


def update_doc_conf(splitted_document, overall_confidence, index):
    splitted_document.overall_confidence = overall_confidence
    flag_modified(splitted_document, "overall_confidence")

    parent_document = splitted_document.parent_document
    predictions_ml = parent_document.predictions_ml
    predictions_ml[index]["overall_confidence"] = overall_confidence
    parent_document.predictions_ml = predictions_ml
    flag_modified(parent_document, "predictions_ml")


def update_status(splitted_documents, status):
    global session
    for splitted_document in splitted_documents:
        splitted_document.status = status


def generate_token(package_id, key):
    """
    Generate a unique encrypted token based on the package ID and timestamp.
    """
    # Get the current timestamp
    timestamp = str(int(time.time()))

    # Combine package_id and timestamp
    token_string = f"{package_id}-{timestamp}"

    # Encrypt the token string using Fernet encryption
    cipher = Fernet(key)
    encrypted_token = cipher.encrypt(token_string.encode())

    return encrypted_token.decode()


def generate_presigned_url(document_id):
    """
    Generates a pre-signed URL for the document stored in MinIO.
    """
    try:
        # Convert UUID to string
        document_id_str = str(document_id)

        # Check if the object exists
        minio_client.stat_object(MINIO_FILES_BUCKET, document_id_str)

        # Generate a pre-signed URL valid for the specified duration
        url = minio_client.presigned_get_object(
            MINIO_FILES_BUCKET,
            document_id_str,
            expires=timedelta(seconds=MINIO_EXPIRATION_TIME),
        )
        print(f"Generated presigned URL: {url}")
        return url
    except S3Error as e:
        print(f"S3Error occurred: {e}")
        if e.code == "NoSuchKey":
            print(f"Document with UUID {document_id_str} not found.")
        else:
            print(f"Failed to generate presigned URL: {e}")
        return


def callback(msg_tuple):
    ch, method, properties, body = msg_tuple
    """
    Function to be called by RabbitMQ consumer loop  arguments:
    body: {'file_id': str, 'document_type': str, 'tenant_id': str, 'subtenant_id': str}
    """
    global session

    print(f" [x] Received {body}")
    queue_item = json.loads(body.decode("UTF-8"))

    splitted_document_uuid = queue_item["file_id"]
    document_type = queue_item["document_type"]
    # Extract tenant information from the message
    tenant_id = queue_item.get('tenant_id')
    subtenant_id = queue_item.get('subtenant_id')
    
    print(f"Validating and routing document type '{document_type}' for tenant: {tenant_id}, subtenant: {subtenant_id}")

    chunk_ids = None

    with db_connector.get_session() as session:
        try:
            splitted_document = get_splitted_document(splitted_document_uuid)
            if splitted_document:
                incoming_package = splitted_document.parent_document.incoming_package
                ready_to_validation, splitted_documents = (
                    all_splitted_docs_are_ready_to_validation(
                        incoming_package, splitted_document.uuid
                    )
                )
                print(
                    f"{splitted_document_uuid} - Status ready - {ready_to_validation}"
                )
                if ready_to_validation:
                    is_valid, validation_report = validate_each(splitted_documents, tenant_id, subtenant_id)
                    validation_report["is_valid"] = is_valid
                    parent_document = get_parent_document(
                        splitted_document.parent_document_uuid
                    )
                    parent_document.validation_report = validation_report
                    flag_modified(parent_document, "validation_report")
                    session.commit()
                    if FORCE_ROUTE:
                        is_valid = 0 if FORCE_ROUTE == 1 else 1
                    print("parent_document.pages_count", parent_document.pages_count)
                    if parent_document.pages_count > MINIMUM_SIZE_OF_LARGE_FILE:
                        is_valid = 0

                        chunk_ids = large_file_handling(session, parent_document)
                        print("chunk_ids", chunk_ids)

                    if is_valid:
                        print("PASS TO UPLOAD")
                        update_status(splitted_documents, "to_upload")
                        parent_document.status = "to_upload"
                        session.commit()  # Commit before sending to RabbitMQ queue, so uploader will check the actual status when receiving the item from the queue.
                        
                        # Pass tenant information to uploader
                        upload_queue_item = {
                            'file_id': splitted_document_uuid,
                            'document_type': document_type,
                            'tenant_id': tenant_id,
                            'subtenant_id': subtenant_id
                        }
                        rmq_service.send_message(RABBITMQ_TO_UPLOAD_QUEUE_NAME, json.dumps(upload_queue_item))

                        token = generate_token(
                            splitted_document.parent_document_uuid, ENCRYPTION_KEY
                        )
                        message = {
                            "document_uuid": str(
                                splitted_document.parent_document_uuid
                            ),  # Convert UUID to string
                            "splitted_documents": parent_document.predictions_ml,
                            "tenant_id": tenant_id,
                            "subtenant_id": subtenant_id
                        }
                        print(message)
                        try:
                            rmq_service.send_message(
                                routing_key=RABBITMQ_TO_BACKEND_OVER_QA_NAME,
                                message=json.dumps(message),
                            )
                            print("Send status to monitoring service ")
                        except Exception as e_send: # Optional: more specific error handling for send
                            print(f"Error sending message to OVER_QA: {e_send}")
                    else:
                        print("SEND TO RABBITMQtoQA QUEUE")

                        # Generate token based on package ID
                        update_status(splitted_documents, "to_qa")
                        parent_document.status = "to_qa"
                        session.commit()
                        token = generate_token(
                            splitted_document.parent_document_uuid, ENCRYPTION_KEY
                        )

                        if not chunk_ids or chunk_ids == []:
                            # Generate a pre-signed URL for the document
                            presigned_url = generate_presigned_url(
                                splitted_document.parent_document_uuid
                            )

                            # Prepare message with token, metadata, classification data, tenant info, and ID
                            # Use tenant-specific queue routing if needed
                            tenant_name = tenant_id or TENANT_NAME  # Use extracted tenant or default
                            message = {
                                "token": token,
                                "document_uuid": str(parent_document.uuid),  # Convert UUID to string
                                "tenant_name": tenant_name,
                                "tenant_id": tenant_id,
                                "subtenant_id": subtenant_id,
                                "filename": parent_document.incoming_package.original_name,
                                "pages_count": int(parent_document.pages_count),
                                "to_qa": METADATA_DISPLAY,
                                "total_number_of_chunks": 1,
                                "chunk": {
                                    "chunk_id": str(uuid7()),
                                    "chunk_number": 1,
                                    "chunk_pages_count": int(parent_document.pages_count),
                                    "chunk_presigned_url": presigned_url,
                                    "chunk_pages_range": [1, parent_document.document_end_page],
                                    # start page is always 1
                                    "chunk_metadata": parent_document.metadata_ml,
                                    "chunk_classification_data": parent_document.predictions_ml
                                }
                            }
                            print(message)

                            try:
                                # Use tenant-aware queue routing
                                qa_queue_name = get_tenant_aware_queue_name(tenant_id, RABBITMQ_TO_BACKEND_QA_QUEUE_NAME)
                                rmq_service.send_message(
                                    routing_key=qa_queue_name,
                                    message=json.dumps(message),
                                )
                                print(f"Sent message to QA queue: {qa_queue_name}")
                            except Exception as e_send: # Optional
                                print(f"Error sending message to QA_QUEUE: {e_send}")
                        else:
                            tenant_name = tenant_id or TENANT_NAME  # Use extracted tenant or default
                            for chunk_number, chunk_id in enumerate(chunk_ids, start=1):
                                print("chunk_id", chunk_id)
                                chunk = (
                                    session.query(DocumentChunk)
                                    .filter_by(uuid=chunk_id)
                                    .first()
                                )
                                presigned_url = generate_presigned_url(chunk_id)
                                # Prepare message with token, metadata, classification data, tenant info, and ID
                                message = {
                                    "token": token,
                                    "document_uuid": str(parent_document.uuid),  # Convert UUID to string
                                    "tenant_name": tenant_name,
                                    "tenant_id": tenant_id,
                                    "subtenant_id": subtenant_id,
                                    "filename": parent_document.incoming_package.original_name,
                                    "pages_count": int(parent_document.pages_count),
                                    "to_qa": METADATA_DISPLAY,
                                    "total_number_of_chunks": len(chunk_ids),
                                    "chunk": {
                                        "chunk_id": str(chunk_id),
                                        "chunk_number": chunk_number,
                                        "chunk_pages_count": int(chunk.pages_count),
                                        "chunk_presigned_url": presigned_url,
                                        "chunk_pages_range": [int(chunk.parent_document_start_page),
                                                              int(chunk.parent_document_end_page)],
                                        "chunk_metadata": chunk.metadata_ml,
                                        "chunk_classification_data": chunk.predictions_ml
                                    }
                                }

                                print(message)

                                try:
                                    # Use tenant-aware queue routing
                                    qa_queue_name = get_tenant_aware_queue_name(tenant_id, RABBITMQ_TO_BACKEND_QA_QUEUE_NAME)
                                    rmq_service.send_message(
                                        routing_key=qa_queue_name,
                                        message=json.dumps(message),
                                    )
                                    print(f"Sent chunk message to QA queue: {qa_queue_name}")
                                except Exception as e_send: # Optional
                                    print(f"Error sending message to QA_QUEUE (chunk): {e_send}")
                else:
                    print("Waiting for other documents to be processed")
            else:
                print("No splitted doc in db. Skipping...")

            rmq_service.safe_ack(ch, method.delivery_tag)
        except Exception as e:
            session.rollback()
            print(f"Error: {e}")


if not RABBITMQ_TO_VALIDATE_QUEUE_NAME:
    raise ValueError("RABBITMQ_TO_VALIDATE_QUEUE_NAME environment variable is not set.")

rmq_service.read_messages(RABBITMQ_TO_VALIDATE_QUEUE_NAME, callback)
# monitor = MonitorService(PROJECT_NAME, APP_NAME, "validate_route", API_HOST, API_PORT)
# monitor.start_monitoring()
# monitor.update_status(Status.UP)
rmq_service.run()
