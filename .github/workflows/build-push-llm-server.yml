name: Build and Push LLM Server Image to AWS ECR

on:
  push:
    branches:
      - main

env:
  AWS_REGION:               ${{ vars.AWS_REGION }}
  REGISTRY_URL:             ${{ vars.REGISTRY_URL }}
  REPOSITORY_NAME:          ${{ vars.REPOSITORY_NAME }}

jobs:
  build-and-push:
    name: Build and Push LLM Server
    runs-on: ubuntu-latest

    steps:
      # ───── Image tag = first 8 chars of commit SHA ───────────────────────
      - name: Compose image tag
        run: echo "IMAGE_TAG=${GITHUB_SHA::8}" >> "$GITHUB_ENV"

      # ───── Enable Buildx --------------------------------------------------
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # ───── AWS credentials (only on “push” runs) --------------------------
      - name: Configure AWS credentials
        if: ${{ github.event_name == 'push' }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id:     ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region:            ${{ env.AWS_REGION }}
          role-to-assume:        ${{ secrets.AWS_DEPLOYER_ROLE }}


      # ───── Login to ECR ---------------------------------------------------
      - name: Login to Amazon ECR
        if: ${{ github.event_name == 'push' }}
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          push: ${{ github.event_name == 'push' }}
          tags: |
            ${{ env.REGISTRY_URL }}/${{ env.REPOSITORY_NAME }}:${{ env.IMAGE_TAG }}
