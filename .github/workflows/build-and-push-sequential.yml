name: Build and Push Images Sequentially to AWS ECR

on:
  push:
    branches: [ multitenant ]
  workflow_dispatch:

env:
  AWS_REGION: us-east-2  # Change to your AWS region
  ECR_REGISTRY: "112623991000"
  PADDLE_BASE_IMAGE: docker.io/paddlepaddle/paddle:2.6.1-gpu-cuda11.7-cudnn8.4-trt8.4@sha256:3c91a0a7aac1bf56eb98c2c4b6c64c055fde8b7b4cfdfb95b7d2ddc786d859bf

jobs:
  sequential-build:
    runs-on:
      - codebuild-github-runner-ml-pipeline-${{ github.run_id }}-${{ github.run_attempt }}
    timeout-minutes: 480  # Increased timeout for sequential builds
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-region: ${{ env.AWS_REGION }}
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}

      - name: Display current AWS identity
        run: |
          aws sts get-caller-identity

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
        with:
          registries: "112623991000"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build Downloader
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./downloader/Dockerfile
          push: ${{ github.event_name != 'pull_request' }}
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/downloader:${{ github.sha }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new

      # - name: Move cache after Downloader
      #   run: |
      #     rm -rf /tmp/.buildx-cache
      #     mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      # # Build PaddlePaddle-based images
      # - name: Build Uploader
      #   uses: docker/build-push-action@v4
      #   with:
      #     context: .
      #     file: ./uploader/Dockerfile
      #     push: ${{ github.event_name != 'pull_request' }}
      #     tags: |
      #       ${{ steps.login-ecr.outputs.registry }}/uploader:${{ github.sha }}
      #     cache-from: type=local,src=/tmp/.buildx-cache
      #     cache-to: type=local,dest=/tmp/.buildx-cache-new

      # - name: Move cache after Uploader
      #   run: |
      #     rm -rf /tmp/.buildx-cache
      #     mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      # - name: Build Splitter
      #   uses: docker/build-push-action@v4
      #   with:
      #     context: .
      #     file: ./splitter/Dockerfile
      #     push: ${{ github.event_name != 'pull_request' }}
      #     tags: |
      #       ${{ steps.login-ecr.outputs.registry }}/splitter:${{ github.sha }}
      #     cache-from: type=local,src=/tmp/.buildx-cache
      #     cache-to: type=local,dest=/tmp/.buildx-cache-new

      # - name: Move cache after Splitter
      #   run: |
      #     rm -rf /tmp/.buildx-cache
      #     mv /tmp/.buildx-cache-new /tmp/.buildx-cache
      # - name: Build Classifier
      #   uses: docker/build-push-action@v4
      #   with:
      #     context: .
      #     file: ./classifier/Dockerfile
      #     push: ${{ github.event_name != 'pull_request' }}
      #     tags: |
      #       ${{ steps.login-ecr.outputs.registry }}/classifier:${{ github.sha }}
      #     build-args: --progress=plain
      #     cache-from: type=local,src=/tmp/.buildx-cache
      #     cache-to: type=local,dest=/tmp/.buildx-cache-new

      # - name: Move cache after Classifier
      #   run: |
      #     rm -rf /tmp/.buildx-cache
      #     mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      # - name: Build Metadata Extractor
      #   uses: docker/build-push-action@v4
      #   with:
      #     context: .
      #     file: ./metadata_extractor/Dockerfile
      #     push: ${{ github.event_name != 'pull_request' }}
      #     tags: |
      #       ${{ steps.login-ecr.outputs.registry }}/metadata-extractor:${{ github.sha }}
      #     build-args: --progress=plain
      #     cache-from: type=local,src=/tmp/.buildx-cache
      #     cache-to: type=local,dest=/tmp/.buildx-cache-new

      # - name: Move cache after Metadata Extractor
      #   run: |
      #     rm -rf /tmp/.buildx-cache
      #     mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      # Build PaddlePaddle-based images
      # - name: Build Uploader
      #   uses: docker/build-push-action@v4
      #   with:
      #     context: .
      #     file: ./uploader/Dockerfile
      #     push: ${{ github.event_name != 'pull_request' }}
      #     tags: |
      #       ${{ steps.login-ecr.outputs.registry }}/uploader:${{ github.sha }}
      #     cache-from: type=local,src=/tmp/.buildx-cache
      #     cache-to: type=local,dest=/tmp/.buildx-cache-new      

      - name: Build Validate and Route
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./validate_and_route/Dockerfile
          push: ${{ github.event_name != 'pull_request' }}
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/validate-route:${{ github.sha }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new

      # - name: Move cache after Validate and Route
      #   run: |
      #     rm -rf /tmp/.buildx-cache
      #     mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      - name: Build Metadata Post-Processor
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./metadata_postprocessor/Dockerfile
          push: ${{ github.event_name != 'pull_request' }}
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/metadata-post-processor:${{ github.sha }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new

      - name: Move cache after Metadata Post-Processor
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      # - name: Build QA Post-Processor
      #   uses: docker/build-push-action@v4
      #   with:
      #     context: .
      #     file: ./qa_post_processor/Dockerfile
      #     push: ${{ github.event_name != 'pull_request' }}
      #     tags: |
      #       ${{ steps.login-ecr.outputs.registry }}/qa-post-processor:${{ github.sha }}
      #     cache-from: type=local,src=/tmp/.buildx-cache
      #     cache-to: type=local,dest=/tmp/.buildx-cache-new

      # - name: Move cache after QA Post-Processor
      #   run: |
      #     rm -rf /tmp/.buildx-cache
      #     mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      # - name: Build Data Cleaner
      #   uses: docker/build-push-action@v4
      #   with:
      #     context: ./data_cleaner
      #     push: ${{ github.event_name != 'pull_request' }}
      #     tags: |
      #       ${{ steps.login-ecr.outputs.registry }}/data-cleaner:${{ github.sha }}
      #     cache-from: type=local,src=/tmp/.buildx-cache
      #     cache-to: type=local,dest=/tmp/.buildx-cache-new

      # - name: Move cache after Data Cleaner
      #   run: |
      #     rm -rf /tmp/.buildx-cache
      #     mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      # - name: Build Select and Populate
      #   uses: docker/build-push-action@v4
      #   with:
      #     context: ./select_and_populate
      #     push: ${{ github.event_name != 'pull_request' }}
      #     tags: |
      #       ${{ steps.login-ecr.outputs.registry }}/select-and-populate:${{ github.sha }}
      #     cache-from: type=local,src=/tmp/.buildx-cache
      #     cache-to: type=local,dest=/tmp/.buildx-cache-new

      # - name: Final cache cleanup
      #   run: |
      #     rm -rf /tmp/.buildx-cache
      #     mv /tmp/.buildx-cache-new /tmp/.buildx-cache