#!/bin/sh
set -e

FINAL_MODEL_NAME="mistral-nemo:12b-instruct-2407-q5_K_M_medical_assistant"
MODELFILE_PATH="/app/Modelfile"

echo "Starting Ollama service setup..."

ollama serve &
OLLAMA_PID=$!

echo "Waiting for Ollama server to be ready..."
until curl -s http://localhost:11434/ > /dev/null; do
  echo "Ollama not up yet, sleeping..."
  sleep 1
done
echo "Ollama server is up."


echo "Attempting to create model '$FINAL_MODEL_NAME' from $MODELFILE_PATH..."

ollama create "$FINAL_MODEL_NAME" -f "$MODELFILE_PATH"
if [ $? -eq 0 ]; then
  echo "Model '$FINAL_MODEL_NAME' created successfully (or already existed and was updated)."
  echo "Base model (e.g., mistral-nemo:12b-instruct-2407-q5_K_M) should have been pulled if necessary."
else
  echo "Failed to create model '$FINAL_MODEL_NAME'. Check Modelfile and base model availability. Exiting."
  kill $OLLAMA_PID
  exit 1
fi

echo "Ollama model setup complete. Final model list:"
ollama list

echo "Stopping temporary Ollama server..."
kill $OLLAMA_PID
wait $OLLAMA_PID || true

echo "Starting Ollama server in foreground..."
exec ollama serve