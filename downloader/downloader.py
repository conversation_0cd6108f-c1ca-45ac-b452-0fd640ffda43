import re
import paramiko
import os
import sys
import io
import json
import pika
import traceback
import img2pdf
import fitz
import tempfile
import subprocess
import zipfile
import extract_msg
import uuid
import magic
import time
import threading
import ssl
from typing import List, Optional, Tuple, Any
import requests
from azure.servicebus import ServiceBusClient, ServiceBusReceivedMessage
import boto3
from botocore.exceptions import ClientError
import http.server
import socketserver
from http import HTTPStatus

from email import policy
from email.parser import BytesParser
from shutil import which
from minio import Minio
from time import sleep
from datetime import datetime, timezone, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from uuid6 import uuid7

from pipeline_utils.rabbitmq_connector import PikaServiceFactory
from pipeline_utils.database_connector import DBConnector
from pipeline_utils.tenant_utils import TenantInfoExtractor, ChannelType, default_tenant_extractor

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from models import Document, IncomingPackage, RawIncomingPackages, Tenant, Subtenant  # Ensure models.py contains these classes
from pipeline_utils.monitoring import MonitorService, Status

# Get configuration from environment variables instead of config.yml
# Project and app configuration
PROJECT_NAME = os.environ.get('PROJECT_NAME', '')
APP_NAME = os.environ.get('APP_NAME', '')
TENANT_NAME = os.environ.get('TENANT_NAME', '')

# MinIO configuration
MINIO_URI = os.environ.get('MINIO_URI', '127.0.0.1:9015')
MINIO_ACCESS_KEY = os.environ.get('MINIO_ACCESS_KEY', '')
MINIO_SECRET_KEY = os.environ.get('MINIO_SECRET_KEY', '')
MINIO_FILES_BUCKET = os.environ.get('MINIO_FILES_BUCKET', 'from-sftp')
MINIO_SECURE = os.environ.get('MINIO_SECURE', 'true').lower() == 'true'

# SFTP Remote configuration
SFTP_DISABLED = os.environ.get('SFTP_DISABLED', 'true').lower() == 'true'
SFTP_REMOTE_HOST = os.environ.get('SFTP_REMOTE_HOST', '127.0.0.1')
SFTP_REMOTE_PORT = int(os.environ.get('SFTP_REMOTE_PORT', '2222'))
SFTP_REMOTE_USER = os.environ.get('SFTP_REMOTE_USER', '')
SFTP_REMOTE_PASSWORD = os.environ.get('SFTP_REMOTE_PASSWORD', '')
SFTP_REMOTE_PATH = os.environ.get('SFTP_REMOTE_PATH', 'Documents')

# RabbitMQ configuration
RABBITMQ_HOST = os.environ.get('RABBITMQ_HOST', 'localhost')
RABBITMQ_PORT = int(os.environ.get('RABBITMQ_PORT', '5682'))
RABBITMQ_USERNAME = os.environ.get('RABBITMQ_USERNAME', '')
RABBITMQ_PASSWORD = os.environ.get('RABBITMQ_PASSWORD', '')
RABBITMQ_TO_CLASSIFY_QUEUE_NAME = os.environ.get('RABBITMQ_TO_CLASSIFY_QUEUE_NAME', 'to_classify')

# Remote RabbitMQ configuration
REMOTE_RABBITMQ_HOST = os.environ.get('REMOTE_RABBITMQ_HOST', '')
REMOTE_RABBITMQ_PORT = int(os.environ.get('REMOTE_RABBITMQ_PORT', '5671'))
REMOTE_RABBITMQ_VHOST = os.environ.get('REMOTE_RABBITMQ_VHOST', '/')
REMOTE_RABBITMQ_USERNAME = os.environ.get('REMOTE_RABBITMQ_USERNAME', '')
REMOTE_RABBITMQ_PASSWORD = os.environ.get('REMOTE_RABBITMQ_PASSWORD', '')
REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME = os.environ.get('REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME', '')

# PostgreSQL configuration
PGSQL_HOST = os.environ.get('PGSQL_HOST', 'localhost')
PGSQL_PORT = int(os.environ.get('PGSQL_PORT', '5438'))
PGSQL_USERNAME = os.environ.get('PGSQL_USERNAME', '')
PGSQL_PASSWORD = os.environ.get('PGSQL_PASSWORD', '')
PGSQL_DB_NAME = os.environ.get('PGSQL_DB_NAME', '')

# Remote PostgreSQL configuration
PGSQL_HOST_REMOTE = os.environ.get('PGSQL_HOST_REMOTE', '')
PGSQL_PORT_REMOTE = int(os.environ.get('PGSQL_PORT_REMOTE', '5432'))
PGSQL_USERNAME_REMOTE = os.environ.get('PGSQL_USERNAME_REMOTE', '')
PGSQL_PASSWORD_REMOTE = os.environ.get('PGSQL_PASSWORD_REMOTE', '')
PGSQL_DB_NAME_REMOTE = os.environ.get('PGSQL_DB_NAME_REMOTE', '')
PGSQL_SSL_MODE_REMOTE = os.environ.get('PGSQL_SSL_MODE_REMOTE', 'require')

# Downloader configuration
SUPPORTED_EXTENTIONS = os.environ.get('SUPPORTED_EXTENTIONS', 'pdf,tif,tiff,rtf,docx').split(',')
SUPPORTED_EXTENTIONS_CONTAINERS = os.environ.get('SUPPORTED_EXTENTIONS_CONTAINERS', '').split(',')
if SUPPORTED_EXTENTIONS_CONTAINERS == ['']: 
    SUPPORTED_EXTENTIONS_CONTAINERS = []
SUPPORTED_EXTENTIONS_DOCUMENTS_PREPROCESS = os.environ.get('SUPPORTED_EXTENTIONS_DOCUMENTS_PREPROCESS', 'tif,tiff,rtf,docx').split(',')
CHANNEL = os.environ.get('CHANNEL', 'sftp')

# Monitoring configuration
API_HOST = os.environ.get('MONITOR_HOST', 'http://_address_here_')
API_PORT = os.environ.get('MONITOR_PORT', '')

# Service Bus configuration
SERVICE_BUS_CONNECTION_STRING = os.environ.get('SERVICE_BUS_CONNECTION_STRING', '')
TOPIC_NAME = os.environ.get('TOPIC_NAME', '')
SUBSCRIPTION_NAME = os.environ.get('SUBSCRIPTION_NAME', '')
SERVICE_BUS_QUEUE_NAME = os.environ.get('SERVICE_BUS_QUEUE_NAME', '')

# S3 configuration
S3_BUCKET_NAME = os.environ.get('S3_BUCKET_NAME', '')
S3_REGION = os.environ.get('S3_REGION', 'us-east-1')
S3_ENDPOINT_URL = os.environ.get('S3_ENDPOINT_URL', None)
if S3_ENDPOINT_URL == "null" or S3_ENDPOINT_URL == "None":
    S3_ENDPOINT_URL = None

# SQS configuration
SQS_QUEUE_URL = os.environ.get('SQS_QUEUE_URL', '')
SQS_REGION = os.environ.get('SQS_REGION', 'us-east-1')
SQS_ACCESS_KEY_ID = os.environ.get('SQS_ACCESS_KEY_ID', '')
SQS_SECRET_ACCESS_KEY = os.environ.get('SQS_SECRET_ACCESS_KEY', '')
SQS_MAX_MESSAGES = int(os.environ.get('SQS_MAX_MESSAGES', '1'))
SQS_WAIT_TIME = int(os.environ.get('SQS_WAIT_TIME', '5'))

# Tenant configuration
DEFAULT_TENANT_ID = os.environ.get('DEFAULT_TENANT_ID', 'default')
DEFAULT_SUBTENANT_ID = os.environ.get('DEFAULT_SUBTENANT_ID', 'default')
TENANT_EXTRACTION_ENABLED = os.environ.get('TENANT_EXTRACTION_ENABLED', 'true').lower() == 'true'

# SSL configuration for RabbitMQ
REMOTE_SSL_CAFILE_PATH = os.environ.get('REMOTE_SSL_CAFILE_PATH', '')
REMOTE_SSL_CERTFILE_PATH = os.environ.get('REMOTE_SSL_CERTFILE_PATH', '')
REMOTE_SSL_KEYFILE_PATH = os.environ.get('REMOTE_SSL_KEYFILE_PATH', '')

context = None
if REMOTE_SSL_CAFILE_PATH and REMOTE_SSL_CERTFILE_PATH and REMOTE_SSL_KEYFILE_PATH:
    try:
        context = ssl.create_default_context(cafile=REMOTE_SSL_CAFILE_PATH)
        context.load_cert_chain(certfile=REMOTE_SSL_CERTFILE_PATH,
                                keyfile=REMOTE_SSL_KEYFILE_PATH)
        print("SSL context for RabbitMQ configured successfully.")
    except Exception as e:
        print(f"Error configuring SSL context for RabbitMQ: {e}")
        context = None
else:
    print("SSL configuration not found in environment variables. Proceeding without SSL for RabbitMQ.")

# **THIS is the important line** – only wrap the context when it exists
ssl_options = pika.SSLOptions(context) if context else None

Base = declarative_base()
RemoteBase = declarative_base()

# Initialize DB connectors for local and remote databases using DBConnector
local_db_connector = DBConnector(
    pgsql_host=PGSQL_HOST,
    pgsql_port=PGSQL_PORT,
    pgsql_username=PGSQL_USERNAME,
    pgsql_password=PGSQL_PASSWORD,
    pgsql_db_name=PGSQL_DB_NAME
)

remote_db_connector = DBConnector(
    pgsql_host=PGSQL_HOST_REMOTE,
    pgsql_port=PGSQL_PORT_REMOTE,
    pgsql_username=PGSQL_USERNAME_REMOTE,
    pgsql_password=PGSQL_PASSWORD_REMOTE,
    pgsql_db_name=PGSQL_DB_NAME_REMOTE,
    pgsql_sslmode=PGSQL_SSL_MODE_REMOTE
)

# Create tables if not already created.
Base.metadata.create_all(local_db_connector.get_engine())
RemoteBase.metadata.create_all(remote_db_connector.get_engine())

SessionRemoteDB = remote_db_connector.get_session
SessionLocalDB = local_db_connector.get_session

# Log database connection information
print("=== DATABASE CONNECTION INFO ===")
print(f"Local Database (Document records):")
print(f"  - Host: {PGSQL_HOST}")
print(f"  - Port: {PGSQL_PORT}")
print(f"  - Database: {PGSQL_DB_NAME}")
print(f"  - Username: {PGSQL_USERNAME}")
print(f"  - Connection String: postgresql://{PGSQL_USERNAME}:***@{PGSQL_HOST}:{PGSQL_PORT}/{PGSQL_DB_NAME}")

print(f"Remote Database (RawIncomingPackages):")
print(f"  - Host: {PGSQL_HOST_REMOTE}")
print(f"  - Port: {PGSQL_PORT_REMOTE}")
print(f"  - Database: {PGSQL_DB_NAME_REMOTE}")
print(f"  - Username: {PGSQL_USERNAME_REMOTE}")
print(f"  - SSL Mode: {PGSQL_SSL_MODE_REMOTE}")
print(f"  - Connection String: postgresql://{PGSQL_USERNAME_REMOTE}:***@{PGSQL_HOST_REMOTE}:{PGSQL_PORT_REMOTE}/{PGSQL_DB_NAME_REMOTE}?sslmode={PGSQL_SSL_MODE_REMOTE}")
print("=== DATABASE CONNECTION INFO END ===")


class Utils:
    def find_libreoffice_executable(self):
        """
        Returns path to the libreoffice executable.

        Returns:
            string: path to libreoffice executable; None: if libreoffice executable was not found
        Note:
            sudo apt-get install libreoffice --no-install-recommends
        """
        # Potential executable names for LibreOffice across platforms
        potential_names = ['libreoffice', 'libreoffice.exe', 'soffice', 'soffice.exe']

        # Check each potential executable to see if it's in the PATH
        for name in potential_names:
            if which(name) is not None:
                return name  # Return the first match found

        # If no executable is found in the PATH, attempt common installation paths
        potential_paths = [
            "C:\\Program Files\\LibreOffice\\program\\",  # Default Windows path
            "C:\\Program Files (x86)\\LibreOffice\\program\\",
            # Default Windows path for 32-bit LibreOffice on 64-bit system
            "/usr/bin/",  # Common Unix/Linux path
            "/Applications/LibreOffice.app/Contents/MacOS/",  # Default macOS path
        ]

        for path in potential_paths:
            for name in potential_names:
                full_path = os.path.join(path, name)
                if os.path.isfile(full_path):
                    return full_path  # Return the first full path found

        # If no executable was found, return None
        return None

    def extract_files_from_zip(self, zip_bytes, extensions):
        """
        Extracts files from ZIP byte object.

        Args:
            zip_bytes (bytes): ZIP byte object
            extensions (list): List of strings with extensions which should be extracted

        Returns:
            List[dict]: A list of dictionaries, each containing data byte object, file_extension and the original file name.
        """
        extracted_files = []
        try:
            # Try to open the zip file from the byte stream
            with zipfile.ZipFile(io.BytesIO(zip_bytes), 'r') as zip_ref:
                for file_info in zip_ref.infolist():
                    if any(file_info.filename.lower().endswith(ext) for ext in extensions):
                        with zip_ref.open(file_info) as file:
                            data = file.read()
                            extracted_files.append({
                                'byte_object': data,
                                'original_name': file_info.filename.split('.')[0],
                                'file_extension': '.' + file_info.filename.split('.')[-1]
                            })
        except zipfile.BadZipFile as e:
            # Catch the error if the file is not a valid zip file and log the error
            print(f"Error extracting zip file: {e}")
        return extracted_files

    def extract_files_from_eml(self, eml_bytes, extensions):
        """
        Extracts files from EML byte object.

        Args:
            msg_bytes (bytes): EML byte object
            extensions (list): List of strings with extentions which should be extracted

        Returns:
            List[dict]: A list of dictionaries, each containing data byte object, file_extension and the original file name.
        """
        extracted_attachments = []

        # Parse the .eml file
        msg = BytesParser(policy=policy.default).parsebytes(eml_bytes)

        # Iterate through the parts of the email to find attachments
        for part in msg.walk():
            # Check if the part is an attachment
            if part.get_content_disposition() == "attachment":
                filename = part.get_filename()
                if filename and any(filename.lower().endswith(ext) for ext in extensions):
                    data = part.get_payload(decode=True)
                    extracted_attachments.append({
                        'byte_object': data,
                        'original_name': filename.split('.')[0],
                        'file_extension': '.' + filename.split('.')[-1].lower()
                    })

        return extracted_attachments

    def extract_files_from_msg(self, msg_bytes, extensions):
        """
        Extracts files from MSG byte object.

        Args:
            msg_bytes (bytes): MSG byte object
            extensions (list): List of strings with extentions which should be extracted

        Returns:
            List[dict]: A list of dictionaries, each containing data byte object, file_extension and the original file name.
        """
        extracted_attachments = []

        with tempfile.NamedTemporaryFile(delete=False, suffix='.msg') as temp_msg:
            temp_msg.write(msg_bytes)
            temp_msg.flush()  # Ensure data is written to disk
            # Open the .msg file
            with extract_msg.Message(temp_msg.name) as msg:
                # Iterate through attachments
                for attachment in msg.attachments:
                    filename = attachment.longFilename or attachment.shortFilename
                    if filename and any(filename.lower().endswith(ext) for ext in extensions):
                        data = attachment.data
                        extracted_attachments.append({
                            'byte_object': data,
                            'original_name': filename.split('.')[0],
                            'file_extension': '.' + filename.split('.')[-1].lower()
                        })

        return extracted_attachments

    def pdf_can_be_opened(self, pdf_bytes):
        """
        Check if a PDF, represented as a bytes object, can be opened (is not password protected etc.).

        Args:
            pdf_bytes (bytes): The PDF data as a bytes object.

        Returns:
            bool: True if the PDF can be opened, False otherwise.
        """
        try:
            # Check MIME type
            mime = magic.Magic(mime=True)
            mime_type = mime.from_buffer(pdf_bytes[:2048])
            if mime_type != 'application/pdf':
                return False

            # Open the PDF
            with fitz.open(stream=pdf_bytes, filetype="pdf") as doc:
                # Check if the document is encrypted
                if doc.is_encrypted:
                    return False

                # Try rendering the first page to ensure it can be processed
                page = doc.load_page(0)  # Load first page
                pix = page.get_pixmap()

                # Check if the pixmap has non-zero dimensions
                if pix.width == 0 or pix.height == 0:
                    print("Rendering check failed: pixmap has zero dimensions.")
                    return False

                return True
        except fitz.PasswordRequired:
            print("The PDF is password-protected.")
            return False
        except fitz.FileDataError:
            print("The PDF is corrupted or not a valid PDF.")
            return False
        except Exception as e:
            print(f"Unexpected error opening PDF: {e}")
            traceback.print_exc()
            return False


class Converter:
    def __init__(self):
        utils = Utils()
        self.libreoffice_executable = utils.find_libreoffice_executable()

    def convert_tiff_to_pdf(self, tiff_bytes):
        """
        Converts a TIFF image byte object to a PDF byte object.

        Args:
            tiff_bytes (bytes): Byte object containing the TIFF image

        Returns:
            bytes: Byte object containing the converted PDF, or None if conversion fails.
        """
        tiff_bytes_io = io.BytesIO(tiff_bytes)
        try:
            # Attempt to convert using img2pdf
            print("Converting tiff to pdf using img2pdf...")
            pdf_bytes = img2pdf.convert(tiff_bytes_io)
            return pdf_bytes
        except Exception as e:
            print(f"img2pdf conversion failed: {e}. Attempting conversion with PIL...")
            # Reset the stream position before attempting fallback conversion
            tiff_bytes_io.seek(0)
            try:
                from PIL import Image  # Importing Pillow for fallback conversion
                with Image.open(tiff_bytes_io) as img:
                    pdf_bytes_io = io.BytesIO()
                    # Save the image as PDF using PIL
                    img.save(pdf_bytes_io, format="PDF")
                    return pdf_bytes_io.getvalue()
            except Exception as pil_e:
                print(f"PIL conversion failed: {pil_e}")
                return None

    def convert_rtf_to_pdf(self, rtf_bytes):
        """
        Converts a RTF image byte object to a PDF byte object.

        Args:
            tiff_bytes (bytes): Byte object containing the RTF

        Returns:
            bytes: Byte object containing the converted PDF
        """
        with tempfile.NamedTemporaryFile(delete=False, suffix='.rtf') as temp_rtf, \
                tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_pdf:

            temp_rtf.write(rtf_bytes)
            temp_rtf.flush()  # Ensure data is written to disk

            # Construct the command to convert the RTF file to PDF using LibreOffice
            cmd = [self.libreoffice_executable, '--convert-to', 'pdf', '--outdir', temp_pdf.name.rsplit(os.sep, 1)[0],
                   temp_rtf.name, '--headless']

            try:
                print(f"Executing command: {cmd} ...")
                subprocess.run(cmd, check=True)
                output_pdf_path = temp_rtf.name.rsplit('.', 1)[0] + '.pdf'
                with open(output_pdf_path, 'rb') as pdf_file:
                    pdf_bytes = pdf_file.read()
            except subprocess.CalledProcessError as e:
                print(f"Error during conversion rtf to pdf: {e}")
                return None
            except FileNotFoundError:
                print(
                    "LibreOffice command not found. Please ensure LibreOffice is installed and accessible from your PATH.")
                return None
            except Exception as e:
                print(f"During conversion rtf to pdf an unexpected error occurred: {e}")
                return None

        os.unlink(temp_rtf.name)
        os.remove(output_pdf_path)  # Cleanup

        return pdf_bytes

    def convert_docx_to_pdf(self, docx_bytes):
        """
        Converts a DOCX byte object to a PDF byte object.

        Args:
            docx_bytes (bytes): Byte object containing the DOCX

        Returns:
            bytes: Byte object containing the converted PDF
        """
        with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_docx, \
                tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_pdf:

            temp_docx.write(docx_bytes)
            temp_docx.flush()  # Ensure data is written to disk

            # Construct the command to convert the DOCX file to PDF using LibreOffice
            cmd = [self.libreoffice_executable, '--convert-to', 'pdf', '--outdir', temp_pdf.name.rsplit(os.sep, 1)[0],
                   temp_docx.name, '--headless']

            try:
                print(f"Executing command: {cmd} ...")
                subprocess.run(cmd, check=True)
                output_pdf_path = temp_docx.name.rsplit('.', 1)[0] + '.pdf'
                with open(output_pdf_path, 'rb') as pdf_file:
                    pdf_bytes = pdf_file.read()
            except subprocess.CalledProcessError as e:
                print(f"Error during conversion docx to pdf: {e}")
                return None
            except FileNotFoundError:
                print(
                    "LibreOffice command not found. Please ensure LibreOffice is installed and accessible from your PATH.")
                return None
            except Exception as e:
                print(f"During conversion docx to pdf an unexpected error occurred: {e}")
                return None

        os.unlink(temp_docx.name)
        os.remove(output_pdf_path)  # Cleanup

        return pdf_bytes

    def convert_doc_to_pdf(self, doc_bytes):
        """
        Converts a DOC byte object to a PDF byte object.

        Args:
            doc_bytes (bytes): Byte object containing the DOC

        Returns:
            bytes: Byte object containing the converted PDF
        """
        with tempfile.NamedTemporaryFile(delete=False, suffix='.doc') as temp_doc, \
                tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_pdf:

            temp_doc.write(doc_bytes)
            temp_doc.flush()  # Ensure data is written to disk

            # Construct the command to convert the DOC file to PDF using LibreOffice
            cmd = [self.libreoffice_executable, '--convert-to', 'pdf', '--outdir', temp_pdf.name.rsplit(os.sep, 1)[0],
                   temp_doc.name, '--headless']

            try:
                print(f"Executing command: {cmd} ...")
                subprocess.run(cmd, check=True)
                output_pdf_path = temp_doc.name.rsplit('.', 1)[0] + '.pdf'
                with open(output_pdf_path, 'rb') as pdf_file:
                    pdf_bytes = pdf_file.read()
            except subprocess.CalledProcessError as e:
                print(f"Error during conversion doc to pdf: {e}")
                return None
            except FileNotFoundError:
                print(
                    "LibreOffice command not found. Please ensure LibreOffice is installed and accessible from your PATH.")
                return None
            except Exception as e:
                print(f"During conversion doc to pdf an unexpected error occurred: {e}")
                return None

        os.unlink(temp_doc.name)
        os.remove(output_pdf_path)  # Cleanup

        return pdf_bytes


class FileProcessor():
    def __init__(self):
        self.utils = Utils()
        self.converter = Converter()
        self.converters = {
            "tiff": self.converter.convert_tiff_to_pdf,
            "tif": self.converter.convert_tiff_to_pdf,
            "rtf": self.converter.convert_rtf_to_pdf,
            "docx": self.converter.convert_docx_to_pdf,
            "doc": self.converter.convert_doc_to_pdf
        }
        self.extractors = {
            "zip": self.utils.extract_files_from_zip,
            "eml": self.utils.extract_files_from_eml,
            "msg": self.utils.extract_files_from_msg
        }

    def process_file(self, byte_object, original_name, file_extension):
        """
        Process a file (convert to PDF, extract from archive, etc.) recursively.

        Args:
            byte_object (bytes or _io.BytesIO): The file content.
            original_name (str): The original file name.
            file_extension (str): The file extension.

        Returns:
            List[dict]: A list of dictionaries, each containing the PDF byte object and the original file name.
        """
        processed_files = []  # To store the results

        try:
            if isinstance(byte_object, io.BytesIO):
                byte_object = byte_object.getvalue()

            # Process files that should be unpacked as the first step
            if file_extension.lower() in SUPPORTED_EXTENTIONS_CONTAINERS:
                extracted_files = []
                extracted_files += self.extractors[file_extension](byte_object, SUPPORTED_EXTENTIONS)
                for item in extracted_files:
                    # Recursively convert/extract each file
                    processed_files += self.process_file(item['byte_object'], item['original_name'],
                                                         item['file_extension'].lstrip('.'))
            # Process files that should be converted to PDF only
            elif file_extension.lower() in SUPPORTED_EXTENTIONS_DOCUMENTS_PREPROCESS and file_extension.lower() != 'pdf':
                if file_extension.lower() in self.converters:
                    pdf_bytes = self.converters[file_extension](byte_object)
                    if pdf_bytes:
                        processed_files.append(
                            {'byte_object': pdf_bytes, 'original_name': original_name, 'original_ext': file_extension})
                else:
                    print(f"No converter found for file extension: {file_extension}")
            # If the file is a PDF, check if it can be opened
            elif file_extension.lower() == 'pdf':
                if self.utils.pdf_can_be_opened(byte_object):
                    processed_files.append(
                        {'byte_object': byte_object, 'original_name': original_name, 'original_ext': file_extension})
                else:
                    print(f"Cannot open PDF file: {original_name}.{file_extension}")
            else:
                print(f'Cannot process file {original_name}.{file_extension}')
        except Exception as e:
            print(f"Error processing file {original_name}.{file_extension}: {e}")
            traceback.print_exc()

        return processed_files


class SFTPDownloader:
    def __init__(self):
        if SFTP_DISABLED:
            print("SFTP is disabled. Skipping SFTP connection initialization.")
            self.ssh_client_remote = None
            self.ftp = None
            self.files = []
            self.ignored_files = set()
            return

        self.ssh_client_remote = paramiko.SSHClient()
        self.ssh_client_remote.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.ssh_client_remote.connect(
            hostname=SFTP_REMOTE_HOST,
            port=SFTP_REMOTE_PORT,
            username=SFTP_REMOTE_USER,
            password=SFTP_REMOTE_PASSWORD,
            look_for_keys=False
        )
        self.ftp = self.ssh_client_remote.open_sftp()
        self.files = []
        self.ignored_files = set()  # Set to store filenames that should be ignored

    def file_is_ready(self, file_path):
        if SFTP_DISABLED:
            return False
        utime = self.ftp.stat(file_path).st_mtime
        last_modified = datetime.fromtimestamp(utime)
        if (datetime.now() - last_modified) > timedelta(seconds=5):
            return True
        return False

    def __get_full_path(self, filename):
        return os.path.join(SFTP_REMOTE_PATH, filename).replace("\\", "/")

    def refresh_queue(self):
        if SFTP_DISABLED:
            self.files = []
            return
        # Filter out files that are not ready or already in ignored_files
        files = self.ftp.listdir(SFTP_REMOTE_PATH)
        self.files = [
            file for file in files
            if self.file_is_ready(self.__get_full_path(file)) and file not in self.ignored_files
        ]

    def queue_is_not_empty(self):
        return True if self.files else False

    def get_next_file(self):
        if SFTP_DISABLED:
            return None, None
        filename = self.files.pop()
        file_path = self.__get_full_path(filename)
        retries = 3
        delay = 7  # seconds

        for attempt in range(retries):
            try:
                print(f"Attempting to download file: {file_path}")
                fo = io.BytesIO()
                start_time = time.time()  # Record start time
                self.ftp.getfo(file_path, fo)  # Download the file
                end_time = time.time()  # Record end time
                download_time = end_time - start_time  # Calculate duration

                if download_time > 60 and retries == 3:
                    print(f"Download took {download_time:.2f} seconds, exceeding threshold. Ignoring file {file_path}.")
                    self.ignored_files.add(filename)  # Mark file as ignored
                    return None, file_path
                else:
                    fo.seek(0)
                    byte_object = fo.read()
                    # Determine file extension (without the dot)
                    ext = os.path.splitext(filename)[1].lstrip('.').lower()

                    # If the file is of a supported type, check its processability.
                    if ext in (
                            SUPPORTED_EXTENTIONS + SUPPORTED_EXTENTIONS_CONTAINERS + SUPPORTED_EXTENTIONS_DOCUMENTS_PREPROCESS):
                        if not self.check_file_processability(byte_object, ext):
                            print(f"File {file_path} cannot be processed. Adding to ignored_files.")
                            self.ignored_files.add(filename)
                            return None, file_path

                    print(f"File {file_path} downloaded successfully")
                    return byte_object, file_path

            except paramiko.SFTPError as e:
                print(f"Attempt {attempt + 1} failed with SFTP error: {e}")
                if attempt < retries - 1:
                    time.sleep(delay)
                else:
                    print(f"Failed to download {file_path} after {retries} attempts due to SFTP errors. Ignoring file.")
                    self.ignored_files.add(filename)
                    return None, file_path
            except IOError as e:
                print(f"Attempt {attempt + 1} failed with IO error: {e}")
                if attempt < retries - 1:
                    time.sleep(delay)
                else:
                    print(f"Failed to download {file_path} after {retries} attempts due to IO errors. Ignoring file.")
                    self.ignored_files.add(filename)
                    return None, file_path
            except Exception as e:
                print(f"An unexpected error occurred: {e}")
                if attempt < retries - 1:
                    time.sleep(delay)
                else:
                    print(
                        f"Failed to download {file_path} after {retries} attempts due to unexpected errors. Ignoring file.")
                    self.ignored_files.add(filename)
                    return None, file_path

        print(f"Exhausted retries for {file_path} without a successful download. Ignoring file.")
        self.ignored_files.add(filename)
        return None, file_path

    def check_file_processability(self, byte_object, ext):
        """
        Checks whether the downloaded file (represented as bytes) can be processed
        based on its extension.

        Args:
            byte_object (bytes): The file's data.
            ext (str): File extension (without the dot).

        Returns:
            bool: True if the file is processable, False otherwise.
        """
        # For PDF files: check if the PDF can be opened.
        if ext == 'pdf':
            return Utils().pdf_can_be_opened(byte_object)
        # For container files (e.g., zip, eml, msg): attempt extraction.
        elif ext in SUPPORTED_EXTENTIONS_CONTAINERS:
            extractor = FileProcessor().extractors.get(ext)
            if extractor:
                extracted_files = extractor(byte_object, SUPPORTED_EXTENTIONS)
                return bool(extracted_files)
            else:
                return False
        # For documents that require preprocessing: attempt conversion.
        elif ext in SUPPORTED_EXTENTIONS_DOCUMENTS_PREPROCESS:
            converter_func = FileProcessor().converters.get(ext)
            if converter_func:
                converted_pdf = converter_func(byte_object)
                return converted_pdf is not None
            else:
                return False
        # For other types, assume processable.
        return True

    def remove_file(self, file_path):
        if SFTP_DISABLED:
            return
        # Check if the file exists before trying to remove it
        try:
            self.ftp.stat(file_path)  # This will raise IOError if file does not exist
        except IOError:
            print(f"File {file_path} does not exist on SFTP server.")
            return
        try:
            self.ftp.remove(file_path)
            print(f"File {file_path} removed successfully.")
        except FileNotFoundError:
            # The file might have been removed already or wasn't there to begin with
            print(f"File {file_path} not found while attempting to remove. It may have already been deleted.")
        except Exception as e:
            print(f"An unexpected error occurred while trying to remove {file_path}: {e}")


class ServicebusDownloader:
    """
    A class to handle downloading files from Azure Service Bus messages.

    Attributes:
        servicebus_client (ServiceBusClient): The Service Bus client instance.
        messages_content (List[dict]): List to store message content.
        messages (List[ServiceBusReceivedMessage]): List to store received Service Bus messages.
    """

    def __init__(self):
        """
        Initializes the ServicebusDownloader with a connection string to the Azure Service Bus.
        """
        self.servicebus_client: ServiceBusClient = ServiceBusClient.from_connection_string(
            conn_str=SERVICE_BUS_CONNECTION_STRING,
            logging_enable=True
        )
        self.messages_content: List[dict] = []
        self.messages: List[ServiceBusReceivedMessage] = []

    def start_receiver(self):
        if CHANNEL == 'servicebus_topic':
            self.receiver = self.servicebus_client.get_subscription_receiver(
                topic_name=TOPIC_NAME,
                subscription_name=SUBSCRIPTION_NAME
            )
        else:
            self.receiver = self.servicebus_client.get_queue_receiver(
                queue_name=SERVICE_BUS_QUEUE_NAME
            )

    def refresh_queue(self):
        if self.receiver:
            self.messages = self.receiver.receive_messages(max_message_count=1, max_wait_time=5)

    def queue_is_not_empty(self) -> bool:
        """
        Checks if there are any messages in the queue.

        Returns:
            bool: True if there are messages in the queue, False otherwise.
        """
        return bool(self.messages)

    def download_file(self, url: str, blob_name: str) -> Tuple[Optional[bytes], Optional[str]]:
        """
        Downloads a file from the given URL.

        Args:
            url (str): The URL of the file to download.
            blob_name (str): The name of the blob (file).

        Returns:
            Tuple[Optional[bytes], Optional[str]]: The downloaded file as bytes and the blob name,
                                                            or (None, None) if the download fails.
        """
        retries: int = 3
        delay: int = 5
        for attempt in range(retries):
            try:
                print(f"Attempting to download file: {url}")
                response = requests.get(url)
                response.raise_for_status()
                byte_object = response.content
                print(f"File {blob_name} downloaded successfully.")
                return byte_object, blob_name
            except requests.Timeout as e:
                print(f"Attempt {attempt + 1} failed with timeout error: {e}")
                if attempt < retries - 1:
                    time.sleep(delay)
            except requests.ConnectionError as e:
                print(f"Attempt {attempt + 1} failed with connection error: {e}")
                if attempt < retries - 1:
                    time.sleep(delay)
            except requests.RequestException as e:
                if response.status_code == 404:
                    print(f"File not found: {url}")
                    break
                elif response.status_code == 403:
                    print(f"Access denied: {url}")
                    break
                elif response.status_code == 410:
                    print(f"File is gone: {url}")
                    break
                else:
                    print(f"Attempt {attempt + 1} failed with error: {e}")
                    if attempt < retries - 1:
                        time.sleep(delay)
            except Exception as e:
                print(f"Unexpected error occurred: {e}")
                if attempt < retries - 1:
                    time.sleep(delay)
        print(f"Failed to download the file {blob_name} after {retries} attempts.")
        return None, None

    def store_message_content(self, blob_name: str, message_content: dict) -> None:
        """
        Stores the content of the message for further processing.

        Args:
            blob_name (str): The name of the blob (file).
            message_content (dict): The content of the message.
        """
        self.messages_content.append({
            "blob_name": blob_name,
            "message_content": message_content
        })


class SQSDownloader:
    """
    A class to handle downloading files from AWS SQS messages.

    Attributes:
        sqs_client: The SQS client instance.
        messages_content (List[dict]): List to store message content.
        messages (List[dict]): List to store received SQS messages.
    """

    def __init__(self):
        """
        Initializes the SQSDownloader with AWS SQS configuration.
        """
        # Configure SQS client
        session = boto3.Session(
            aws_access_key_id=SQS_ACCESS_KEY_ID if SQS_ACCESS_KEY_ID else None,
            aws_secret_access_key=SQS_SECRET_ACCESS_KEY if SQS_SECRET_ACCESS_KEY else None,
            region_name=SQS_REGION
        )
        self.sqs_client = session.client('sqs')
        self.messages_content: List[dict] = []
        self.messages: List[dict] = []
        self.queue_url = SQS_QUEUE_URL

    def refresh_queue(self):
        """Refresh the queue by polling for new messages"""
        try:
            response = self.sqs_client.receive_message(
                QueueUrl=self.queue_url,
                MaxNumberOfMessages=SQS_MAX_MESSAGES,
                WaitTimeSeconds=SQS_WAIT_TIME,
                MessageAttributeNames=['All']
            )
            
            self.messages = response.get('Messages', [])
            if self.messages:
                print(f"Received {len(self.messages)} messages from SQS queue")
        except Exception as e:
            print(f"Error refreshing SQS queue: {e}")
            traceback.print_exc()
            self.messages = []

    def queue_is_not_empty(self) -> bool:
        """
        Checks if there are any messages in the queue.

        Returns:
            bool: True if there are messages in the queue, False otherwise.
        """
        return bool(self.messages)

    def download_file(self, url: str, file_name: str) -> Tuple[Optional[bytes], Optional[str]]:
        """
        Downloads a file from the given URL.

        Args:
            url (str): The URL of the file to download.
            file_name (str): The name of the file.

        Returns:
            Tuple[Optional[bytes], Optional[str]]: The downloaded file as bytes and the file name,
                                                           or (None, None) if the download fails.
        """
        retries: int = 3
        delay: int = 5
        for attempt in range(retries):
            try:
                print(f"Attempting to download file: {url}")
                response = requests.get(url, timeout=30)
                response.raise_for_status()
                byte_object = response.content
                print(f"File {file_name} downloaded successfully from SQS message.")
                return byte_object, file_name
            except requests.Timeout as e:
                print(f"Attempt {attempt + 1} failed with timeout error: {e}")
                if attempt < retries - 1:
                    time.sleep(delay)
            except requests.ConnectionError as e:
                print(f"Attempt {attempt + 1} failed with connection error: {e}")
                if attempt < retries - 1:
                    time.sleep(delay)
            except requests.RequestException as e:
                if hasattr(e, 'response') and e.response is not None:
                    if e.response.status_code == 404:
                        print(f"File not found: {url}")
                        break
                    elif e.response.status_code == 403:
                        print(f"Access denied: {url}")
                        break
                    elif e.response.status_code == 410:
                        print(f"File is gone: {url}")
                        break
                print(f"Attempt {attempt + 1} failed with error: {e}")
                if attempt < retries - 1:
                    time.sleep(delay)
            except Exception as e:
                print(f"Unexpected error occurred: {e}")
                if attempt < retries - 1:
                    time.sleep(delay)
        print(f"Failed to download the file {file_name} after {retries} attempts.")
        return None, None

    def delete_message(self, message: dict) -> None:
        """
        Deletes a message from the SQS queue.

        Args:
            message (dict): The SQS message to delete.
        """
        try:
            receipt_handle = message.get('ReceiptHandle')
            if receipt_handle:
                self.sqs_client.delete_message(
                    QueueUrl=self.queue_url,
                    ReceiptHandle=receipt_handle
                )
                print("SQS message deleted successfully.")
            else:
                print("No receipt handle found for SQS message deletion.")
        except Exception as e:
            print(f"Error deleting SQS message: {e}")
            traceback.print_exc()

    def store_message_content(self, file_name: str, message_content: dict) -> None:
        """
        Stores the content of the message for further processing.

        Args:
            file_name (str): The name of the file.
            message_content (dict): The content of the message.
        """
        self.messages_content.append({
            "file_name": file_name,
            "message_content": message_content
        })


def send_status_to_monitoring(file_id_str, filename):
    message = {
        "document_uuid": file_id_str,
        "filename": filename,
        "tenant_name": TENANT_NAME,
        "status": "in"
    }
    print(f"Preparing to send status update to monitoring queue '{REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME}'")
    print(f"Status message content: {message}")
    remote_rmq_service_factory = PikaServiceFactory(
        host=REMOTE_RABBITMQ_HOST,
        port=REMOTE_RABBITMQ_PORT,
        virtual_host=REMOTE_RABBITMQ_VHOST,
        username=REMOTE_RABBITMQ_USERNAME,
        password=REMOTE_RABBITMQ_PASSWORD,
        ssl_options=ssl_options
    )
    remote_rmq_service = remote_rmq_service_factory.create_service()
    remote_rmq_service.start()
    try:
        remote_rmq_service.send_message(routing_key=REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME,
                                        message=json.dumps(message))
        print(f"Successfully sent status update to monitoring queue '{REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME}'")
    except Exception as e:
        print(f"Failed to send status update to monitoring queue: {str(e)}")
        traceback.print_exc()
    finally:
        remote_rmq_service.stop()


def concatenate(pdf_files, original_name, original_package_extension):
    merged_pdf = fitz.open()
    for pdf_file in pdf_files:
        byte_object = pdf_file['byte_object']
        doc = fitz.open(stream=byte_object, filetype="pdf")
        merged_pdf.insert_pdf(doc)
        doc.close()

    doc_bytes = merged_pdf.write()
    pdf_file = {
        'byte_object': io.BytesIO(doc_bytes).read(),
        'original_name': original_name,
        'original_ext': original_package_extension
    }
    merged_pdf.close()

    return [pdf_file]


def process_files(byte_object, original_name, original_package_extension, processor):
    try:
        pdf_files = processor.process_file(byte_object, original_name, original_package_extension[1:])
        pdf_files_name = []
        for pdf_file in pdf_files:
            pdf_files_name.append(f"{pdf_file['original_name']}.{pdf_file['original_ext']}")
        print(f"Extracted {len(pdf_files)} files from package: {pdf_files_name}")
        if len(pdf_files) > 1:
            pdf_files = concatenate(pdf_files, original_name, original_package_extension[1:])
            print(f"Concatenated into single pdf.")
        elif len(pdf_files) == 1:
            pdf_files[0]['original_name'] = original_name
            pdf_files[0]['original_ext'] = original_package_extension
        return pdf_files
    except Exception as e:
        print(f"Error processing files from package {original_name}.{original_package_extension}: {e}")
        traceback.print_exc()
        return []


def ensure_tenant_exists(session, tenant_id, subtenant_id):
    """
    Ensure that tenant and subtenant records exist in the database.
    Creates them if they don't exist.
    
    Args:
        session: Database session
        tenant_id: The tenant ID
        subtenant_id: The subtenant ID
    
    Returns:
        bool: True if tenant/subtenant exist or were created successfully
    """
    try:
        # Check if tenant exists
        tenant = session.query(Tenant).filter_by(tenant_id=tenant_id).first()
        
        if not tenant:
            # Create tenant
            tenant = Tenant(
                tenant_id=tenant_id,
                tenant_name=f"Tenant {tenant_id}",
                description=f"Auto-created tenant for {tenant_id}",
                created_date=datetime.now(timezone.utc),
                active=True
            )
            session.add(tenant)
            print(f"Created tenant: {tenant_id}")
        
        # Check if subtenant exists
        subtenant = session.query(Subtenant).filter_by(
            subtenant_id=subtenant_id,
            tenant_id=tenant_id
        ).first()
        
        if not subtenant:
            # Create subtenant
            subtenant = Subtenant(
                subtenant_id=subtenant_id,
                tenant_id=tenant_id,
                subtenant_name=f"Subtenant {subtenant_id}",
                description=f"Auto-created subtenant for {subtenant_id}",
                created_date=datetime.now(timezone.utc),
                active=True
            )
            session.add(subtenant)
            print(f"Created subtenant: {subtenant_id} for tenant: {tenant_id}")
        
        return True
        
    except Exception as e:
        print(f"Error ensuring tenant exists: {e}")
        traceback.print_exc()
        return False


def handle_package(session, filename, byte_object, original_package_extension, channel, processor,
                   raw_incoming_package_id, message_content=None, file_path=None, tenant_id=None, subtenant_id=None):
    try:
        # If tenant information is not provided, extract it from the data
        if tenant_id is None or subtenant_id is None:
            tenant_id, subtenant_id = extract_tenant_info_from_data(
                channel_name=channel,
                file_path=file_path,
                message_content=message_content,
                message_properties=message_content.get('applicationProperties') if message_content else None
            )
        
        # Ensure tenant and subtenant exist in the database
        if not ensure_tenant_exists(session, tenant_id, subtenant_id):
            print(f"Failed to ensure tenant exists for {tenant_id}/{subtenant_id}. Using default tenant.")
            tenant_id, subtenant_id = DEFAULT_TENANT_ID, DEFAULT_SUBTENANT_ID
        
        original_package_name, original_package_extension = os.path.splitext(filename)
        pdf_files = process_files(byte_object, original_package_name, original_package_extension, processor)
        if not pdf_files:
            print(f"No files were extracted from the package {filename}.")
            return
        print("raw_incoming_package_id:", raw_incoming_package_id)
        
        # Pass tenant information to the incoming package record
        incoming_package = add_incoming_package_record_to_db(
            session, filename, channel, raw_incoming_package_id, message_content, 
            tenant_id, subtenant_id
        )
        print(f"New package id: {incoming_package.uuid}")

        minio_client = Minio(
            MINIO_URI,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=MINIO_SECURE
        )
        print(f"Initializing MinIO client with endpoint: {MINIO_URI.replace('http://', '').replace('https://', '')}")
        print(f"Using bucket: {MINIO_FILES_BUCKET}")
        
        if not minio_client.bucket_exists(MINIO_FILES_BUCKET):
            print(f"Bucket {MINIO_FILES_BUCKET} does not exist. Creating it...")
            minio_client.make_bucket(MINIO_FILES_BUCKET)
            print(f"Successfully created bucket {MINIO_FILES_BUCKET}")
        else:
            print(f"Bucket {MINIO_FILES_BUCKET} exists")

        for pdf_file in pdf_files:
            byte_object = pdf_file['byte_object']
            original_name = pdf_file['original_name']
            original_ext = pdf_file['original_ext']
            print(f"Processing PDF document {original_name}, original extension: {original_ext}")

            file_id = uuid7()
            file_id_str = f'{file_id}'
            print(f"Generated file ID: {file_id_str}")

            send_status_to_monitoring(file_id_str, original_name)

            fo = io.BytesIO()
            fo.write(byte_object)
            fo.seek(0)
            file_size = fo.getbuffer().nbytes
            size_in_megabytes = file_size / (1024 * 1024)
            print(f"File size: {size_in_megabytes:.2f} MB")

            try:
                print(f"Uploading file {file_id_str} to MinIO bucket {MINIO_FILES_BUCKET}")
                minio_client.put_object(MINIO_FILES_BUCKET, file_id_str, fo, file_size)
                print(f"Successfully uploaded file {file_id_str} to MinIO")
            except Exception as e:
                print(f"Failed to upload file {file_id_str} to MinIO: {str(e)}")
                traceback.print_exc()
                continue

            add_document_record_to_db(session, file_id, original_name, incoming_package, size_in_megabytes)

            # Verify the document was actually saved to the database
            print(f"=== DATABASE VERIFICATION START ===")
            try:
                # Query the database to verify the document exists
                saved_doc = session.query(Document).filter_by(uuid=file_id).first()
                if saved_doc:
                    print(f"✓ Document verification SUCCESSFUL:")
                    print(f"  - Found document with uuid: {saved_doc.uuid}")
                    print(f"  - Document filename: {saved_doc.file}")
                    print(f"  - Document status: {saved_doc.status}")
                    print(f"  - Document tenant_id: {saved_doc.tenant_id}")
                    print(f"  - Document subtenant_id: {saved_doc.subtenant_id}")
                    print(f"  - Document file_size: {saved_doc.file_size}")
                    
                    # Test UUID format conversion (like classifier does)
                    print(f"=== UUID FORMAT TESTING ===")
                    try:
                        # Convert to string and back to UUID (like classifier does)
                        file_id_str = str(file_id)
                        print(f"  - Original file_id: {file_id} (type: {type(file_id)})")
                        print(f"  - file_id as string: {file_id_str}")
                        
                        # Test the conversion that classifier uses
                        converted_uuid = uuid.UUID(file_id_str)
                        print(f"  - Converted UUID: {converted_uuid} (type: {type(converted_uuid)})")
                        print(f"  - UUID comparison (file_id == converted_uuid): {file_id == converted_uuid}")
                        
                        # Test the exact query that classifier uses
                        test_doc = session.query(Document).filter_by(uuid=converted_uuid).first()
                        if test_doc:
                            print(f"  - ✓ Query with converted UUID SUCCESSFUL")
                            print(f"  - Found document: {test_doc.uuid}")
                        else:
                            print(f"  - ✗ Query with converted UUID FAILED")
                            print(f"  - Could not find document with converted UUID: {converted_uuid}")
                        
                    except Exception as uuid_error:
                        print(f"  - ✗ UUID conversion ERROR: {uuid_error}")
                        traceback.print_exc()
                    print(f"=== UUID FORMAT TESTING END ===")
                    
                else:
                    print(f"✗ Document verification FAILED:")
                    print(f"  - Could not find document with uuid: {file_id}")
                    print(f"  - This indicates the document was not saved to the database")
                    
                    # Try to find any documents with similar UUIDs
                    print(f"=== SEARCHING FOR SIMILAR DOCUMENTS ===")
                    try:
                        # Get all documents to see what's in the database
                        all_docs = session.query(Document).limit(5).all()
                        print(f"  - Found {len(all_docs)} recent documents in database:")
                        for i, doc in enumerate(all_docs):
                            print(f"    {i+1}. UUID: {doc.uuid}, File: {doc.file}, Status: {doc.status}")
                    except Exception as search_error:
                        print(f"  - Error searching for documents: {search_error}")
                    print(f"=== SEARCHING FOR SIMILAR DOCUMENTS END ===")
                    
            except Exception as e:
                print(f"✗ Document verification ERROR: {e}")
                traceback.print_exc()
            print(f"=== DATABASE VERIFICATION END ===")

            # Include tenant information in the queue message for downstream processing
            queue_item = {
                'file_id': file_id_str, 
                'filename': original_name,
                'tenant_id': tenant_id,
                'subtenant_id': subtenant_id
            }

            # Publish to RabbitMQ
            print(f"Preparing to send message to RabbitMQ queue '{RABBITMQ_TO_CLASSIFY_QUEUE_NAME}'")
            print(f"Message content: {queue_item}")
            rmq_service_factory = PikaServiceFactory(
                host=RABBITMQ_HOST,
                port=RABBITMQ_PORT,
                username=RABBITMQ_USERNAME,
                password=RABBITMQ_PASSWORD,
                ssl_options=None
            )
            rmq_service = rmq_service_factory.create_service()
            rmq_service.start()
            try:
                rmq_service.send_message(routing_key=RABBITMQ_TO_CLASSIFY_QUEUE_NAME, message=json.dumps(queue_item))
                print(f"Successfully sent message to RabbitMQ queue '{RABBITMQ_TO_CLASSIFY_QUEUE_NAME}'")
            except Exception as e:
                print(f"Failed to send message to RabbitMQ: {str(e)}")
                traceback.print_exc()
            finally:
                rmq_service.stop()

    except Exception as e:
        print(f"Error handling package {filename}: {e}")
        traceback.print_exc()
        raise


def add_incoming_package_record_to_db(session, filename, channel, raw_incoming_package_id, message_content=None, 
                                     tenant_id=None, subtenant_id=None):
    package_id = uuid7()
    incoming_package_data = {
        "uuid": package_id,
        "original_name": filename,
        "channel": channel,
        "received_date": datetime.now(timezone.utc),
        "raw_incoming_package_id": raw_incoming_package_id,
        "incoming_data": json.dumps(message_content) if message_content else None,
        "tenant_id": tenant_id or DEFAULT_TENANT_ID,
        "subtenant_id": subtenant_id or DEFAULT_SUBTENANT_ID
    }
    incoming_package = IncomingPackage(**incoming_package_data)
    session.add(incoming_package)
    print(f"Package {incoming_package.uuid} added to local DB with tenant: {tenant_id}, subtenant: {subtenant_id}")
    return incoming_package


def add_document_record_to_db(session, file_id, filename, incoming_package, file_size):
    print(f"=== DATABASE INSERT START ===")
    print(f"Adding document record to database:")
    print(f"  - file_id: {file_id} (type: {type(file_id)})")
    print(f"  - filename: {filename}")
    print(f"  - incoming_package.uuid: {incoming_package.uuid}")
    print(f"  - file_size: {file_size} MB")
    print(f"  - tenant_id: {incoming_package.tenant_id}")
    print(f"  - subtenant_id: {incoming_package.subtenant_id}")
    
    try:
        doc = Document(
            uuid=file_id,
            file=filename,
            incoming_package=incoming_package,
            status='to_classify',
            date=datetime.now(timezone.utc),
            file_size=file_size,
            # Inherit tenant information from the incoming package
            tenant_id=incoming_package.tenant_id,
            subtenant_id=incoming_package.subtenant_id
        )
        print(f"Document object created successfully")
        
        session.add(doc)
        print(f"Document added to session")
        
        # Try to flush to see if there are any immediate errors
        session.flush()
        print(f"Session flushed successfully - no immediate errors")
        
        print(f"=== DATABASE INSERT COMPLETED ===")
        
    except Exception as e:
        print(f"=== DATABASE INSERT ERROR ===")
        print(f"Error adding document to database: {e}")
        print(f"Error type: {type(e)}")
        traceback.print_exc()
        raise


def upload_to_s3_bucket(bucket_name, file_data, destination_blob_name):
    """
    Uploads a file to the specified S3 bucket with error handling and a verification step.

    Args:
        bucket_name (str): The name of the S3 bucket.
        file_data (bytes): The data of the file to upload.
        destination_blob_name (str): The desired name of the file in the bucket.

    Returns:
        bool: True if the file was uploaded and verified successfully, False otherwise.
    """
    destination_blob_name = str(destination_blob_name)
    
    try:
        # Create S3 client using IAM role
        s3_client = boto3.client(
            's3',
            region_name=S3_REGION,
            endpoint_url=S3_ENDPOINT_URL
        )
        
        # Upload the file
        s3_client.put_object(
            Bucket=bucket_name,
            Key=destination_blob_name,
            Body=io.BytesIO(file_data)
        )
        print(f"File uploaded to S3 bucket {bucket_name} as {destination_blob_name}.")
        
        # Verify the upload by downloading and comparing
        response = s3_client.get_object(
            Bucket=bucket_name,
            Key=destination_blob_name
        )
        downloaded_data = response['Body'].read()
        
        if downloaded_data != file_data:
            raise ValueError("Verification failed: Uploaded file content does not match the original.")
        
        print(f"Verification successful for {destination_blob_name}.")
        return True  # Indicate success
    except ClientError as e:
        print(f"Failed to upload {destination_blob_name} to S3 bucket {bucket_name}. Error: {e}")
        traceback.print_exc()
        return False  # Indicate failure
    except Exception as e:
        print(f"Unexpected error uploading {destination_blob_name} to S3 bucket {bucket_name}. Error: {e}")
        traceback.print_exc()
        return False  # Indicate failure

def download_from_s3_bucket(bucket_name, blob_name):
    """
    Downloads a file from the S3 bucket with error handling.
    
    Args:
        bucket_name (str): The name of the S3 bucket.
        blob_name (str): The name of the file in the bucket.
        
    Returns:
        bytes or None: The file data if successful, None otherwise.
    """
    blob_name = str(blob_name)
    
    try:
        # Create S3 client using IAM role
        s3_client = boto3.client(
            's3',
            region_name=S3_REGION,
            endpoint_url=S3_ENDPOINT_URL
        )
        
        # Download the file
        response = s3_client.get_object(
            Bucket=bucket_name,
            Key=blob_name
        )
        file_data = response['Body'].read()
        print(f"File {blob_name} downloaded from S3 bucket {bucket_name}.")
        return file_data
    except ClientError as e:
        print(f"Failed to download {blob_name} from S3 bucket {bucket_name}. Error: {e}")
        traceback.print_exc()
        return None  # Return None to indicate download failure
    except Exception as e:
        print(f"Unexpected error downloading {blob_name} from S3 bucket {bucket_name}. Error: {e}")
        traceback.print_exc()
        return None  # Return None to indicate download failure

def receive_data_from_channel():
    if CHANNEL == 'sftp':
        downloader = SFTPDownloader()
    elif CHANNEL in ['servicebus_topic', 'servicebus_queue']:
        downloader = ServicebusDownloader()
        downloader.start_receiver()
    elif CHANNEL == 'sqs':
        downloader = SQSDownloader()
    else:
        raise ValueError(f"Invalid CHANNEL configuration: {CHANNEL}")

    while True:
        if CHANNEL == 'sftp':
            downloader.refresh_queue()
            while downloader.queue_is_not_empty():
                with remote_db_connector.session_scope() as session_remote:
                    try:
                        byte_object, file_path = downloader.get_next_file()
                        if byte_object:
                            filename = os.path.basename(file_path)
                            
                            # Extract tenant information from SFTP file path
                            tenant_id, subtenant_id = extract_tenant_info_from_data(
                                channel_name='sftp',
                                file_path=file_path
                            )

                            raw_package_id = uuid7()
                            upload_success = upload_to_s3_bucket(S3_BUCKET_NAME, byte_object, raw_package_id)

                            if upload_success:
                                # Ensure tenant exists in remote database before storing
                                with remote_db_connector.session_scope() as session_tenant:
                                    if not ensure_tenant_exists(session_tenant, tenant_id, subtenant_id):
                                        print(f"Failed to ensure tenant exists for {tenant_id}/{subtenant_id}. Using default tenant.")
                                        tenant_id, subtenant_id = DEFAULT_TENANT_ID, DEFAULT_SUBTENANT_ID
                                    
                                    incoming_data = RawIncomingPackages(
                                        id=raw_package_id,
                                        name_of_file=filename,
                                        channel='sftp',
                                        message=None,
                                        received_date=datetime.now(timezone.utc),
                                        status="pending",
                                        tenant_id=tenant_id,
                                        subtenant_id=subtenant_id
                                    )
                                    session_remote.add(incoming_data)
                                    session_remote.commit()

                                    # Verify the entry exists in the database before removing the file
                                    if session_remote.query(RawIncomingPackages).filter_by(id=incoming_data.id).first():
                                        downloader.remove_file(file_path)
                                        print(f"File {filename} successfully added to database and removed from SFTP. Tenant: {tenant_id}, Subtenant: {subtenant_id}")
                                    else:
                                        print("Database insert verification failed. File not removed.")
                            else:
                                print(f"Failed to upload {filename} to S3; skipping database entry.")
                    except Exception as e:
                        print(f"An error occurred in SFTP processing: {e}")
                        traceback.print_exc()
                        session_remote.rollback()
            sleep(10)
            
        elif CHANNEL in ['servicebus_topic', 'servicebus_queue']:
            with downloader.receiver:
                while True:
                    downloader.refresh_queue()
                    while downloader.queue_is_not_empty():
                        with remote_db_connector.session_scope() as session_remote:
                            try:
                                message = downloader.messages.pop(0)
                                print(f"Received ServiceBus message: {message}")
                                message_content = json.loads(str(message))
                                blob_url = message_content.get('sasUri')
                                blob_name = message_content.get('blobName')
                                
                                if blob_url and blob_name:
                                    byte_object, file_path = downloader.download_file(blob_url, blob_name)
                                    if byte_object:
                                        # Extract tenant information from ServiceBus message
                                        tenant_id, subtenant_id = extract_tenant_info_from_data(
                                            channel_name='servicebus',
                                            message_content=message_content,
                                            message_properties=getattr(message, 'application_properties', {})
                                        )
                                        
                                        raw_package_id = uuid7()
                                        upload_success = upload_to_s3_bucket(S3_BUCKET_NAME, byte_object, raw_package_id)
                                        
                                        if upload_success:
                                            # Ensure tenant exists in remote database before storing
                                            with remote_db_connector.session_scope() as session_tenant:
                                                if not ensure_tenant_exists(session_tenant, tenant_id, subtenant_id):
                                                    print(f"Failed to ensure tenant exists for {tenant_id}/{subtenant_id}. Using default tenant.")
                                                    tenant_id, subtenant_id = DEFAULT_TENANT_ID, DEFAULT_SUBTENANT_ID
                                            
                                            incoming_data = RawIncomingPackages(
                                                id=raw_package_id,
                                                name_of_file=blob_name,
                                                channel='servicebus',
                                                message=message_content,
                                                received_date=datetime.now(timezone.utc),
                                                status="pending",
                                                tenant_id=tenant_id,
                                                subtenant_id=subtenant_id
                                            )
                                            session_remote.add(incoming_data)
                                            session_remote.commit()

                                            # Verify the entry exists in the database
                                            if session_remote.query(RawIncomingPackages).filter_by(id=incoming_data.id).first():
                                                downloader.receiver.complete_message(message)
                                                print(f"File {blob_name} successfully added to database and message completed. Tenant: {tenant_id}, Subtenant: {subtenant_id}")
                                            else:
                                                print("Database insert verification failed. Message not completed.")
                                        else:
                                            print(f"Failed to upload {blob_name} to S3; skipping database entry.")
                                    else:
                                        downloader.receiver.abandon_message(message)
                                else:
                                    print("Missing sasUri or blobName in ServiceBus message")
                                    downloader.receiver.abandon_message(message)
                            except Exception as e:
                                print(f"An error occurred in ServiceBus processing: {e}")
                                traceback.print_exc()
                                session_remote.rollback()
                    sleep(10)
                    
        elif CHANNEL == 'sqs':
            while True:
                downloader.refresh_queue()
                while downloader.queue_is_not_empty():
                    with remote_db_connector.session_scope() as session_remote:
                        try:
                            message = downloader.messages.pop(0)
                            print(f"Received SQS message: {message}")
                            
                            # Parse message body
                            message_body = json.loads(message.get('Body', '{}'))
                            message_attributes = message.get('MessageAttributes', {})
                            
                            # Extract file URL and name from message
                            file_url = message_body.get('fileUrl') or message_body.get('url')
                            file_name = message_body.get('fileName') or message_body.get('filename')
                            
                            if file_url and file_name:
                                byte_object, downloaded_file_name = downloader.download_file(file_url, file_name)
                                if byte_object:
                                    # Extract tenant information from SQS message
                                    tenant_id, subtenant_id = extract_tenant_info_from_data(
                                        channel_name='sqs',
                                        message_content=message_body,
                                        message_properties=message_attributes
                                    )
                                    
                                    raw_package_id = uuid7()
                                    upload_success = upload_to_s3_bucket(S3_BUCKET_NAME, byte_object, raw_package_id)
                                    
                                    if upload_success:
                                        # Ensure tenant exists in remote database before storing
                                        with remote_db_connector.session_scope() as session_tenant:
                                            if not ensure_tenant_exists(session_tenant, tenant_id, subtenant_id):
                                                print(f"Failed to ensure tenant exists for {tenant_id}/{subtenant_id}. Using default tenant.")
                                                tenant_id, subtenant_id = DEFAULT_TENANT_ID, DEFAULT_SUBTENANT_ID
                                        
                                        incoming_data = RawIncomingPackages(
                                            id=raw_package_id,
                                            name_of_file=file_name,
                                            channel='sqs',
                                            message=message_body,
                                            received_date=datetime.now(timezone.utc),
                                            status="pending",
                                            tenant_id=tenant_id,
                                            subtenant_id=subtenant_id
                                        )
                                        session_remote.add(incoming_data)
                                        session_remote.commit()

                                        # Verify the entry exists in the database
                                        if session_remote.query(RawIncomingPackages).filter_by(id=incoming_data.id).first():
                                            downloader.delete_message(message)
                                            print(f"File {file_name} successfully added to database and SQS message deleted. Tenant: {tenant_id}, Subtenant: {subtenant_id}")
                                        else:
                                            print("Database insert verification failed. SQS message not deleted.")
                                    else:
                                        print(f"Failed to upload {file_name} to S3; skipping database entry.")
                                else:
                                    print(f"Failed to download file from URL: {file_url}")
                            else:
                                print("Missing fileUrl or fileName in SQS message")
                                print(f"Message body: {message_body}")
                        except Exception as e:
                            print(f"An error occurred in SQS processing: {e}")
                            traceback.print_exc()
                            session_remote.rollback()
                sleep(10)

def process_data_from_db():
    file_processor = FileProcessor()
    while True:
        try:
            # Open both sessions together so that if an exception is raised,
            # both sessions' transactions will be rolled back.
            with remote_db_connector.session_scope() as session_remote, \
                    local_db_connector.session_scope() as session_local:

                unprocessed_data = session_remote.query(RawIncomingPackages).filter_by(status="pending").all()

                for data in unprocessed_data:
                    print(f"Starting processing for package: {data.name_of_file} (Tenant: {data.tenant_id}, Subtenant: {data.subtenant_id})")
                    byte_object = download_from_s3_bucket(S3_BUCKET_NAME, data.id)
                    if byte_object is None:
                        print(f"Skipping {data.name_of_file} due to download failure.")
                        continue

                    # Update package status in the remote database.
                    data.status = "processing"
                    data.start_processing_date = datetime.now(timezone.utc)

                    # Create a pseudo file path for SFTP channels (needed for tenant extraction in handle_package)
                    file_path = None
                    if data.channel == 'sftp':
                        # Reconstruct the file path for tenant extraction
                        if data.tenant_id and data.subtenant_id:
                            file_path = f"{data.tenant_id}/{data.subtenant_id}/{data.name_of_file}"
                        else:
                            file_path = data.name_of_file

                    # Process the package and add the corresponding document record to the local database.
                    # Note: handle_package will use the tenant info from the RawIncomingPackages record
                    # via extract_tenant_info_from_data, but since we already have it, we could optimize this
                    handle_package(
                        session_local,
                        data.name_of_file,
                        byte_object,
                        os.path.splitext(data.name_of_file)[1],
                        data.channel,
                        file_processor,
                        data.id,
                        data.message,
                        file_path,
                        data.tenant_id,
                        data.subtenant_id
                    )
                    
                    # Update the status to completed
                    data.status = "done"
                    data.end_processing_date = datetime.now(timezone.utc)
                    
                # If no exception occurs, both session_remote and session_local commit their changes here.
        except Exception as e:
            print(f"Error in process_data_from_db: {e}")
            traceback.print_exc()
            # Both sessions will be rolled back automatically on exception.
        sleep(10)


def receive_data_from_channel_wrapper():
    while True:
        try:
            receive_data_from_channel()
        except Exception as e:
            print(f"Error in receive_data_from_channel thread: {e}")
            traceback.print_exc()
            # we can add a retry or restart here
        time.sleep(5)


def process_data_from_db_wrapper():
    while True:
        try:
            process_data_from_db()
        except Exception as e:
            print(f"Error in process_data_from_db thread: {e}")
            traceback.print_exc()
            # we can add a retry or restart here
        time.sleep(5)


# Monitoring daemon implementation
class MonitoringHandler(http.server.BaseHTTPRequestHandler):
    def do_POST(self):
        # Parse request path
        if self.path == "/api/v1/update_or_create_service":
            # Get content length
            content_length = int(self.headers['Content-Length'])
            
            # Read the request body
            post_data = self.rfile.read(content_length)
            
            try:
                # Parse JSON data
                data = json.loads(post_data.decode('utf-8'))
                print(f"Received monitoring update: {data}")
                
                # Send successful response
                self.send_response(HTTPStatus.OK)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                
                # Send a mock response
                response = {
                    "status": "success", 
                    "message": "Service status updated successfully"
                }
                self.wfile.write(json.dumps(response).encode('utf-8'))
            except json.JSONDecodeError:
                self.send_response(HTTPStatus.BAD_REQUEST)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Invalid JSON"}).encode('utf-8'))
        else:
            # Handle unknown endpoints
            self.send_response(HTTPStatus.NOT_FOUND)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({"error": "Endpoint not found"}).encode('utf-8'))
    
    def log_message(self, format, *args):
        # Customize logging
        print(f"[MonitoringDaemon] {self.client_address[0]} - {format % args}")

def start_monitoring_daemon(host='127.0.0.1', port=7795, max_retries=3):
    """
    Start the monitoring daemon in a separate thread with retry capability.
    
    Args:
        host (str): The hostname to bind to
        port (int): The port to bind to
        max_retries (int): Maximum number of port retry attempts
        
    Returns:
        threading.Thread: The daemon thread
    """
    def run_server(current_port, retries_left):
        nonlocal daemon_started
        
        try:
            # Use allow_reuse_address to avoid "Address already in use" errors
            socketserver.TCPServer.allow_reuse_address = True
            with socketserver.TCPServer((host, current_port), MonitoringHandler) as httpd:
                daemon_started = True
                print(f"Monitoring daemon started at http://{host}:{current_port}")
                httpd.serve_forever()
        except OSError as e:
            print(f"Failed to start monitoring daemon on port {current_port}: {e}")
            # If port is in use and we have retries left, try the next port
            if e.errno == 98 and retries_left > 0:  # Address already in use
                next_port = current_port + 1
                print(f"Retrying with port {next_port}...")
                run_server(next_port, retries_left - 1)
            else:
                print("Failed to start monitoring daemon after all retries. Monitoring may not work correctly.")
        except Exception as e:
            print(f"Unexpected error starting monitoring daemon: {e}")
            traceback.print_exc()
    
    daemon_started = False
    thread = threading.Thread(target=lambda: run_server(port, max_retries), daemon=True)
    thread.start()
    
    # Give the server a moment to start up
    time.sleep(1)
    
    return thread, daemon_started, port

# Enhanced MonitorService wrapper
class EnhancedMonitorService:
    """
    Wrapper for MonitorService that redirects calls to a local endpoint if remote monitoring is unavailable.
    """
    def __init__(self, monitor_service, local_host='127.0.0.1', local_port=7795):
        self.monitor_service = monitor_service
        self.local_host = local_host
        self.local_port = local_port
        self.remote_failed = False
        
        # Patch the _check_status method to use local endpoint if remote fails
        original_check_status = monitor_service._check_status
        
        def patched_check_status():
            while True:
                status_data = {
                    "application": self.monitor_service.application,
                    "type": "gauge",
                    "replica_id": str(self.monitor_service.service_uuid),
                    "name": f"{self.monitor_service.module_name}_status",
                    "value": self.monitor_service.status.value,
                    "help": f"The metric corresponds to status of {self.monitor_service.module_name} status",
                    "additional_parameters": {
                        "client": self.monitor_service.project_name,
                    }
                }
                
                # Try remote endpoint first (only if we haven't had failures)
                if not self.remote_failed:
                    try:
                        remote_url = f"{self.monitor_service.api_host}:{self.monitor_service.api_port}/api/v1/update_or_create_service"
                        r = requests.post(remote_url, json=status_data, timeout=5)
                        print(f"Monitoring update sent to remote endpoint: {remote_url}")
                        # Reset flag if remote endpoint works again
                        self.remote_failed = False
                    except Exception as e:
                        print(f'Call to remote monitoring API failed: {e}')
                        self.remote_failed = True
                
                # If remote failed, use local endpoint
                if self.remote_failed:
                    try:
                        local_url = f"http://{self.local_host}:{self.local_port}/api/v1/update_or_create_service"
                        r = requests.post(local_url, json=status_data, timeout=5)
                        print(f"Monitoring update sent to local endpoint: {local_url}")
                    except Exception as e:
                        print(f'Call to local monitoring API also failed: {e}')
                
                time.sleep(30)
        
        # Replace the original method with our patched version
        self.monitor_service._check_status = patched_check_status
    
    def start_monitoring(self):
        """Start the monitoring service"""
        self.monitor_service.start_monitoring()
    
    def update_status(self, status):
        """Update the service status"""
        self.monitor_service.update_status(status)


def extract_tenant_info_from_data(channel_name: str, file_path: Optional[str] = None, 
                                 message_content: Optional[dict] = None, 
                                 message_properties: Optional[dict] = None) -> Tuple[Optional[str], Optional[str]]:
    """
    Extract tenant information from various data sources using the TenantInfoExtractor.
    
    Args:
        channel_name: The name of the channel ('sftp', 'servicebus', 'sqs', etc.)
        file_path: File path for SFTP/S3 channels
        message_content: Message body content for ServiceBus/SQS
        message_properties: Message properties/attributes for ServiceBus/SQS
    
    Returns:
        Tuple of (tenant_id, subtenant_id) or (None, None) if not found
    """
    if not TENANT_EXTRACTION_ENABLED:
        return DEFAULT_TENANT_ID, DEFAULT_SUBTENANT_ID
    
    try:
        # Map channel names to ChannelType enum
        channel_mapping = {
            'sftp': ChannelType.SFTP,
            'servicebus_topic': ChannelType.SERVICEBUS_TOPIC,
            'servicebus_queue': ChannelType.SERVICEBUS_QUEUE,
            'servicebus': ChannelType.SERVICEBUS_QUEUE,  # Default ServiceBus to queue
            'sqs': ChannelType.SQS,
            's3': ChannelType.S3
        }
        
        channel_type = channel_mapping.get(channel_name.lower())
        if not channel_type:
            print(f"Unknown channel type: {channel_name}, using default tenant")
            return DEFAULT_TENANT_ID, DEFAULT_SUBTENANT_ID
        
        # Extract tenant information
        tenant_id, subtenant_id = default_tenant_extractor.extract_tenant_info(
            channel=channel_type,
            file_path=file_path,
            message_content=message_content,
            message_properties=message_properties
        )
        
        # Validate and use defaults if extraction failed
        if not default_tenant_extractor.validate_tenant_info(tenant_id, subtenant_id):
            print(f"Invalid tenant info extracted: tenant_id={tenant_id}, subtenant_id={subtenant_id}")
            tenant_id, subtenant_id = DEFAULT_TENANT_ID, DEFAULT_SUBTENANT_ID
        
        if not tenant_id:
            tenant_id = DEFAULT_TENANT_ID
        if not subtenant_id:
            subtenant_id = DEFAULT_SUBTENANT_ID
            
        print(f"Extracted tenant info - tenant_id: {tenant_id}, subtenant_id: {subtenant_id}")
        return tenant_id, subtenant_id
        
    except Exception as e:
        print(f"Error extracting tenant info: {e}")
        traceback.print_exc()
        return DEFAULT_TENANT_ID, DEFAULT_SUBTENANT_ID


if __name__ == '__main__':
    # Start the monitoring daemon first
    # port = int(API_PORT) if API_PORT else 7795
    # monitoring_thread, daemon_started, local_port = start_monitoring_daemon(host='127.0.0.1', port=port)
    # print(f"Started monitoring daemon on port {local_port}")
    
    # Then start the monitoring service with our enhanced wrapper
    # monitor_service = MonitorService(PROJECT_NAME, APP_NAME, 'downloader', API_HOST, API_PORT)
    # # Wrap it with our enhanced service that can fall back to the local daemon
    # monitor = EnhancedMonitorService(monitor_service, local_host='127.0.0.1', local_port=local_port)
    # monitor.start_monitoring()
    # monitor.update_status(Status.UP)

    t1 = threading.Thread(target=receive_data_from_channel_wrapper)
    t2 = threading.Thread(target=process_data_from_db_wrapper)
    t1.start()
    t2.start()

    try:
        while t1.is_alive() and t2.is_alive():
            time.sleep(1)
    except KeyboardInterrupt:
        print("Stopping threads...")
        t1.join()
        t2.join()
